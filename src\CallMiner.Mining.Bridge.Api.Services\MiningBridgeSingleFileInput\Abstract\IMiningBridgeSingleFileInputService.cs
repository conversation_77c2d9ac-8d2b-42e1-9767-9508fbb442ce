﻿using CallMiner.Mining.Bridge.Api.Domain;
namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Defines the contract for a IMiningBridgeSingleFileInputService, which is responsible for processing a mining bridge single file input request.
    /// </summary>
    public interface IMiningBridgeSingleFileInputService
    {
        /// <summary>
        /// Process a mining bridge single input request and inserts a mining contact into the mining system
        /// for Tenants with an FTP account configured for mining.
        /// </summary>
        /// <param name="request">The mining bridge single file input request to process.</param>
        /// <returns>A MiningBridgeSingleFileInputServiceResponse object.</returns>
        MiningBridgeSingleFileInputServiceResponse Process(MiningBridgeSingleFileInputRequest request);

        /// <summary>
        /// Process a mining bridge single input request and inserts a file into the mining system to 
        /// start the metadata and audio pairing process for Tenants configured with an FTP account.
        /// </summary>
        /// <param name="request">RecoveryRequest wit serialized payload of the original context created for Rabbit MQ.</param>
        /// <returns>A MiningBridgeSingleFileInputServiceResponse object.</returns>
        MiningBridgeSingleFileInputServiceResponse ProcessRecovery(RecoveryRequest request);
    }
}