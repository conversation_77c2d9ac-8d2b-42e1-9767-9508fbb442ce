﻿using System;
using System.Collections.Generic;
using System.Text;

namespace CallMiner.Mining.Bridge.Api.Configuration
{
    /// <summary>
    /// Represents the different error Guids used for CallMinerExceptions.
    /// </summary>
    public static class ErrorGuids
    {
        /// <summary>
        /// Invalid or missing configuration error guid.
        /// </summary>
        public static Guid InvalidOrMissingConfiguration = new Guid("28e74ac5-a688-451b-8da2-f101ec541d71");

        /// <summary>
        /// Parameter failed validation error guid.
        /// </summary>
        public static Guid InvalidParameter = new Guid("f1998b3f-cd8c-4ea5-a8f6-3492d0811484");
    }
}
