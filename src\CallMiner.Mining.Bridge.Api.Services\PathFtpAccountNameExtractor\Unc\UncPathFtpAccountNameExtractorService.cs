﻿using CallMiner.Mining.Bridge.Api.Domain;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Responsible for extracting the required tenant details from the path.
    /// </summary>
    public class UncPathFtpAccountNameExtractorService : IPathFtpAccountNameExtractorService
    {

        private readonly IGetTenantIngestionDataService _getTenantIngestionDataService;

        /// <summary>
        /// Initializes a new instance of UncPathFtpAccountNameExtractorService class.
        /// </summary>
        /// <param name="getTenantIngestionDataService">Service used to get all Tenants' ingestion data.</param>
        public UncPathFtpAccountNameExtractorService(IGetTenantIngestionDataService getTenantIngestionDataService)
        {
            _getTenantIngestionDataService = getTenantIngestionDataService;
        }

        /// <summary>
        /// Retrieves all possible path segments including grouped togethered ordered so that
        /// the full path segments come up before the partial path 
        /// </summary>
        /// <param name="path">Path name where file is located.</param>
        /// <returns>List of strings containing partial path segments to match against the known root directories belonging to tenants.</returns>
        private List<string> GetAllPathSegments(string path)
        {
            // Split the path on the directory separator (Defined by the OS to ensure this is OS agnostic)
            return new List<string>(Path.GetDirectoryName(path.Trim()).Split(Path.DirectorySeparatorChar));
        }

        /// <summary>
        /// Extracts the FTP details and tenant api key.
        /// </summary>
        /// <param name="inputPath">Path where the file is stored.</param>
        /// <returns>An implementation of the CallMiner ResultFunctionResponse with a TenantFtpAccountData Result containing
        /// the Tenant API Key and the FtpAccountPath.</returns>
        public PathFtpAccountNameExtractorServiceResponse Extract(string inputPath)
        {

            // Validate the input path exists.
            if (String.IsNullOrWhiteSpace(inputPath))
            {
                return new PathFtpAccountNameExtractorServiceResponse(101);
            }

            // Get the tenant data from the tenant ingestion data service
            var tenantDataResult = _getTenantIngestionDataService.GetAll();

            if (tenantDataResult.HasErrors)
            {
                return new PathFtpAccountNameExtractorServiceResponse(tenantDataResult);
            }

            var tenantData = tenantDataResult.Result;

            // Get all the combinations of path segments
            List<string> pathSegments = GetAllPathSegments(inputPath.ToLower());
            

            for(int i = 0; i < pathSegments.Count; i++)
            {
                string inputDirectoryPath = string.Join(Path.DirectorySeparatorChar.ToString(), pathSegments.GetRange(i, pathSegments.Count - i).ToArray());
                if (tenantData.ContainsKey(inputDirectoryPath))
                {
                    return new PathFtpAccountNameExtractorServiceResponse(new TenantFtpAccountData()
                    {
                        TenantApiKey = tenantData[inputDirectoryPath].TenantApiKey,
                        AudioSourceName = tenantData[inputDirectoryPath].AudioSourceName
                    }, "Success.");
                }
            }

            // If no results were found then we can't match any part of this path up to a known
            // tenant. In this scenario it is likely just a tenant that is not yet in NGM. An error
            // result is returned.
            return new PathFtpAccountNameExtractorServiceResponse(102, new object[] { inputPath });
        }
    }
}
