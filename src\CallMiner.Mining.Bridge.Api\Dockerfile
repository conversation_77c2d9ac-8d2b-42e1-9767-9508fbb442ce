FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine

ARG CALLMINER_VERSION=Undefined
ARG CALLMINER_CHANGESET=Undefined
ARG CALLMINER_BUILDNUMBER=Undefined
ENV CALLMINER_VERSION=${CALLMINER_VERSION}
ENV CALLMINER_CHANGESET=${CALLMINER_CHANGESET}
ENV CALLMINER_BUILDNUMBER=${CALLMINER_BUILDNUMBER}

RUN apk update && \
    apk upgrade --available && \
    apk add krb5-libs sqlite \
    && rm -rf /var/cache/apk/*

COPY . /App
WORKDIR /App

EXPOSE 1925

ENTRYPOINT ["dotnet", "BridgeRestApi.dll", "--console"]