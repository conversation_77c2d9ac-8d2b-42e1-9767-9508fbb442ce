﻿using CallMiner.ServiceFramework.Domain;
using CallMiner.SystemMetadata.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Interface to Visibility service
    /// </summary>
    public interface IVisibilityRequestService
    {
        /// <summary>
        /// Performs tracking of request/content in Visibility
        /// </summary>
        /// <param name="request">Router request</param>
        /// <param name="audioSource">Audio source</param>
        /// <param name="isForceNonMineable">Sets non-mineable for non-whitelisted files</param>
        void TrackRequest(Request request, AudioSource audioSource, bool isForceNonMineable = false);

        /// <summary>
        /// Returns MS SQL index friendly guid (time sorted)
        /// </summary>
        Guid NewCombGuid();
    }
}
