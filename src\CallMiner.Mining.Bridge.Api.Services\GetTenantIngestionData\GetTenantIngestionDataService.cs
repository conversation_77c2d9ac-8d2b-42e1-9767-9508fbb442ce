﻿using CallMiner.Logging;
using CallMiner.SystemMetadata.Domain;
using CallMiner.Runtime.Caching;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using CallMiner.SystemMetadata.SDK;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Responsible for returning a Dictionary<string, TenantIngestionData> for all tenants in the
    /// NGM data store.
    /// </summary>
    public class GetTenantIngestionDataService : IGetTenantIngestionDataService
    {

        private readonly ITenantIngestionService _tenantIngestionService;
        private readonly ICacheProvider _cacheProvider;
        private readonly ILogProvider _logProvider;

        private const string IngestionTenantCacheKey = "ingestiontenantdata";

        /// <summary>
        /// Perminent internal memory caching used to handle failures on the API when cache is empty but after
        /// a successful call to the api has occurred.
        /// </summary>
        private static Dictionary<string, TenantIngestionData> _permanentCaching = null;

        /// <summary>
        /// Initializes a new instance of FindTenantIngestionDataService class.
        /// </summary>
        public GetTenantIngestionDataService(ITenantIngestionService tenantIngestionService,
            ICacheProvider cacheProvider, ILogProvider logProvider)
        {
            _tenantIngestionService = tenantIngestionService;
            _cacheProvider = cacheProvider;
            _logProvider = logProvider;
        }

        /// <summary>
        /// Retrieves a Dictionary<string, TenantIngestionData> containing the TenantFtpAccountName as the key and
        /// the TenantIngestionData as the value for all tenants.
        /// </summary>
        /// <returns>Returns all tenant ingestion data for all tenants formatted in a Dictionary.</returns>
        public GetTenantIngestionDataServiceResponse GetAll()
        {

            // Try to get the tenant data from the cache.
            Dictionary<string, TenantIngestionData> tenantData = (Dictionary<string, TenantIngestionData>)_cacheProvider.Get(IngestionTenantCacheKey);

            if (tenantData == null)
            {
                tenantData = new Dictionary<string, TenantIngestionData>();
                try
                {

                    // Get all of the tenant ingestion data for all tenants in one request.
                    var tenantIngestionDataList = _tenantIngestionService.GetAllTenantsIngestions();

                    // Convert the resulting data set to a Dictionary where the key is the FtpAccountName.

                    foreach ( var tenantIngestionData in tenantIngestionDataList.Where(m => m.IngestionSource != null && ( m.IngestionSource is FtpIngestionSource ) ) )
                    {
                        var ingestionSource = (FtpIngestionSource)tenantIngestionData.IngestionSource;

                        if ( String.IsNullOrWhiteSpace(ingestionSource.SourceDirectoryName.Name) )
                        {
                            //
                            // This should not happen if changes are done through provisionin API. But just in case if a tenant by mistake
                            // is configured to have an empty directoryname then we just skip it so the mining of other tenants is not
                            // incorrectly matched.
                            // TODO: Talk to Drew
                            _logProvider.LogInfo(String.Format("Audio Source assigned to a directory name that is empty or null. TenantApiKey {0}. AudioSourceName: {1}", tenantIngestionData.TenantApiKey, tenantIngestionData.AudioSourceName), null);
                            continue;
                        }

                        if (ingestionSource.SourceDirectoryName.Name.IndexOfAny(Path.GetInvalidPathChars()) > -1 || tenantIngestionData.AudioSourceName.IndexOfAny(Path.GetInvalidPathChars()) > -1)
                        {
                            //
                            // Skipping audio sources that would generate an invalid UNC file name. Those names are valid for 
                            // audio sources that don't use FTP upload only.
                            _logProvider.LogInfo(String.Format("Audio Source assigned has a directory name or audio source name that creates an invalid path. TenantApiKey {0}. AudioSourceName: {1}", tenantIngestionData.TenantApiKey, tenantIngestionData.AudioSourceName), null);
                            continue;
                        }

                        var key = (Path.Combine(ingestionSource.SourceDirectoryName.Name, (ingestionSource.IgnoreAudioSourceName) ? "" : tenantIngestionData.AudioSourceName)).ToLower();

                        if (!tenantData.ContainsKey(key))
                        {
                            tenantData.Add(key, tenantIngestionData);
                        }
                    }

                    // Store the data in the cache for re-use
                    _cacheProvider.Add(IngestionTenantCacheKey, tenantData);

                    // Set the pemanent caching to the latest data set.
                    _permanentCaching = tenantData;

                }
                catch(Exception ex)
                {

                    // If the permanent caching has never been set then throw the error up because
                    // there is no handling that can be done.
                    if (_permanentCaching == null)
                    {
                        throw;
                    }

                    _logProvider.LogException("An exception occurred while attempting to access the mining system metadata api service. Using the previously cached tenantIngestionData", null, ex);

                    // Setup to return the permanent caching.
                    tenantData = _permanentCaching;

                }
            }
            return new GetTenantIngestionDataServiceResponse(tenantData, "Success.");
        }
    }
}
