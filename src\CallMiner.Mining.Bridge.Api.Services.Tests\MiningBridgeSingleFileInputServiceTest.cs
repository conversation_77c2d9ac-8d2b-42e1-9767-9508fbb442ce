﻿using System;
using CallMiner.Logging;
using CallMiner.Mining.Bridge.Api.Domain;
using CallMiner.QueueMessagePublisher;
using CallMiner.ServiceFramework.Domain;
using CallMiner.SystemMetadata.SDK;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using CallMiner.NgmFile.Services;
using System.Collections.Generic;
using CallMiner.ServiceConfiguration.Sections.BridgeFiltering;
using System.IO;
using CallMiner.IO.Unc;

namespace CallMiner.Mining.Bridge.Api.Services.Tests
{
    [TestClass]
    public class MiningBridgeSingleFileInputServiceTest
    {
        private MiningBridgeSingleFileInputService _miningBridgeSingleFileInputService;
        private Mock<IPathFtpAccountNameExtractorService> _pathFtpAccountNameExtractorServiceMock;
        private Mock<IMessagePublisher> _messagePublisherMock;
        private Mock<IMessagePublisher> _messagePublisherWithFailureMock;
        private Mock<IFailoverService> _failOverServiceMock;
        private Mock<IVisibilityRequestService> _visibilityServiceMock;
        private Mock<IAudioSourceLocatorService> _audioSourceLocatorService;
        private Mock<INgmFileWriterService> _ngmFileWriterService;
        private Mock<IXmlMetadataBuilderService> _xmlMetadataBuilderService;
        private Mock<IUncMapperService> _uncMapperService;
        private Mock<ILogProvider> _logProvider;

        [TestInitialize]
        public void Init()
        {
            _pathFtpAccountNameExtractorServiceMock = new Mock<IPathFtpAccountNameExtractorService>();
            _pathFtpAccountNameExtractorServiceMock.Setup(x => x.Extract(It.IsAny<string>()))
                .Returns(new PathFtpAccountNameExtractorServiceResponse(new TenantFtpAccountData
                {
                    TenantApiKey = "tenant1"
                }, "Success"));

            _xmlMetadataBuilderService = new Mock<IXmlMetadataBuilderService>();
            _xmlMetadataBuilderService.Setup(x => x.BuildXmlMetadata(It.IsAny<string>())).Returns("someFilePath.xml");

            _messagePublisherMock = new Mock<IMessagePublisher>();
            _messagePublisherMock.Setup(x => x.Publish(It.IsAny<object>(), It.IsAny<Guid>(), false));

            _messagePublisherWithFailureMock = new Mock<IMessagePublisher>();
            _messagePublisherWithFailureMock.Setup(x => x.Publish(It.IsAny<object>(), It.IsAny<Guid>(), false)).Throws(new Exception());

            _failOverServiceMock = new Mock<IFailoverService>();
            _failOverServiceMock.Setup(x => x.InsertRequest(It.IsAny<MiningBridgeSingleFileInputRequest>()));

            _visibilityServiceMock = new Mock<IVisibilityRequestService>();
            _audioSourceLocatorService = new Mock<IAudioSourceLocatorService>();
            _audioSourceLocatorService.Setup(x => x.LocateAudioSourceByTenantAndSourceName(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(new SystemMetadata.Domain.AudioSource()
                {
                    Id = Guid.NewGuid(),
                    IngestionSource = new SystemMetadata.Domain.LiveVoxIngestionSource()
                    {
                        Type = SystemMetadata.Domain.IngestionSourceType.LiveVox
                    }
                });
            _ngmFileWriterService = new Mock<INgmFileWriterService>();
            _uncMapperService = new Mock<IUncMapperService>();

            _uncMapperService.Setup(m => m.Map(It.IsAny<string>()));

            _logProvider = new Mock<ILogProvider>();

            _miningBridgeSingleFileInputService = new MiningBridgeSingleFileInputService(_pathFtpAccountNameExtractorServiceMock.Object,
                _messagePublisherMock.Object, _failOverServiceMock.Object, _audioSourceLocatorService.Object, _visibilityServiceMock.Object,
                _ngmFileWriterService.Object, new GetFileTypeService(new BridgeFilteringSection()
                {
                    AudioFileExtensions = new HashSet<string>() { ".xml", ".csv", ".zip"},
                    MetadataFileExtensions = new HashSet<string>() { ".wav", ".mp3", ".aug" }
                }), _xmlMetadataBuilderService.Object, _uncMapperService.Object);

        }

        [TestMethod]
        public void MiningBridgeSingleFileInputService_Process_ShouldReturnGuid()
        {
            var request = new MiningBridgeSingleFileInputRequest
            {
                InputFilePath = System.IO.Path.GetTempFileName().Replace(".tmp", ".xml")
            };

            var processResponse = _miningBridgeSingleFileInputService.Process(request);

            Assert.IsFalse(processResponse.HasErrors);
            Assert.IsNotNull(processResponse.Result);
            File.Delete(request.InputFilePath);
        }

        [TestMethod]
        public void MiningBridgeSingleFileInputService_Process_ShouldReturn101()
        {
            const int errorCodeExpected = 101;


            var processResponse = _miningBridgeSingleFileInputService.Process(null);

            Assert.IsTrue(processResponse.HasErrors);
            Assert.AreEqual(errorCodeExpected, processResponse.ErrorCode);
        }

        [TestMethod]
        public void MiningBridgeSingleFileInputService_Process_ShouldReturn102()
        {
            const int errorCodeExpected = 102;

            var request = new MiningBridgeSingleFileInputRequest
            {
                InputFilePath = string.Empty
            };

            var processResponse = _miningBridgeSingleFileInputService.Process(request);

            Assert.IsTrue(processResponse.HasErrors);
            Assert.AreEqual(errorCodeExpected, processResponse.ErrorCode);
        }

        [ExpectedException(typeof(Exception))]
        [TestMethod]
        public void MiningBridgeSingleFileInputService_Process_ShouldCallFailover()
        {
            var request = new MiningBridgeSingleFileInputRequest
            {
                InputFilePath = System.IO.Path.GetTempFileName().Replace(".tmp", ".xml")
            };

            var failOverServiceMock = new Mock<IFailoverService>();
            failOverServiceMock.Setup(x => x.InsertRequest(It.IsAny<MiningBridgeSingleFileInputRequest>()));

            _miningBridgeSingleFileInputService = new MiningBridgeSingleFileInputService(_pathFtpAccountNameExtractorServiceMock.Object,
               _messagePublisherWithFailureMock.Object, failOverServiceMock.Object, _audioSourceLocatorService.Object, _visibilityServiceMock.Object,
                _ngmFileWriterService.Object, new GetFileTypeService(new BridgeFilteringSection()
                {
                    AudioFileExtensions = new HashSet<string>() { ".xml", ".csv", ".zip" },
                    MetadataFileExtensions = new HashSet<string>() { ".wav", ".mp3", ".aug" }
                }), _xmlMetadataBuilderService.Object, _uncMapperService.Object);

            try
            {
               _miningBridgeSingleFileInputService.Process(request);
            }
            catch (Exception)
            {
                failOverServiceMock.Verify(x => x.InsertRequest(It.IsAny<MiningBridgeSingleFileInputRequest>()));
                File.Delete(request.InputFilePath);
                throw;
            }
        }
    }
}
