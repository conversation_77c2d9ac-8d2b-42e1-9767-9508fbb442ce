﻿using System;
using System.IO;
using System.Text;
using System.Xml;

namespace CallMiner.Mining.Bridge.Api.Services
{
    public class XmlMetadataBuilderService : IXmlMetadataBuilderService
    {
        /// <summary>
        /// Builds an xml metadata and writes to the disk. Returns the file path of the new xml file.
        /// </summary>
        /// <param name="audioFilePath">The full file path of the audio file.</param>
        /// <returns>The full file path of the xml file which is placed in the same directory as the media file.</returns>
        public string BuildXmlMetadata(string audioFilePath)
        {
            // Extract the filename without the extension.
            string audioFileName = Path.GetFileName(audioFilePath);

            if (audioFileName.EndsWith(".pgp", StringComparison.OrdinalIgnoreCase))
                audioFileName = Path.GetFileNameWithoutExtension(audioFileName);

            var baseFileName = Path.GetFileNameWithoutExtension(audioFileName);

            // generate an output filename which matches the audio filename with an xml extension.
            string outputFileName = Path.Combine(Path.GetDirectoryName(audioFilePath), $"{baseFileName}.xml");

            // create an xml writer on the memory stream.
            using (var xmlWriter = new XmlTextWriter(outputFileName, Encoding.UTF8))
            {
                xmlWriter.WriteProcessingInstruction("xml", "version='1.0' encoding='UTF-8'");
                xmlWriter.WriteStartElement("recordings");
                xmlWriter.WriteStartElement("recording");
                xmlWriter.WriteElementString("CustomerCorrelationId", audioFileName);
                xmlWriter.WriteElementString("Date", DateTime.UtcNow.ToString());
                xmlWriter.WriteElementString("Filename", audioFileName);
                xmlWriter.WriteEndElement();
                xmlWriter.WriteEndElement();
            }

            return outputFileName;
        }
    }
}
