﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Provides a contract for an xml metadata builder service which is responsible for building an xml
    /// string containing a generic piece of metadata based on the file path of an audio file.
    /// </summary>
    public interface IXmlMetadataBuilderService
    {

        /// <summary>
        /// Builds an xml metadata and writes to the disk. Returns the file path of the new xml file.
        /// </summary>
        /// <param name="audioFilePath">The full file path of the audio file.</param>
        /// <returns>The full file path of the xml file which is placed in the same directory as the media file.</returns>
        string BuildXmlMetadata(string audioFilePath);

    }
}
