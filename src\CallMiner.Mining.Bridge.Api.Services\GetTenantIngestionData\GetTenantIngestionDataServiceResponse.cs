﻿using CallMiner.SystemMetadata.Domain;
using System.Collections.Generic;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Represents the response for the GetTenantIngestionDataService.
    /// </summary>
    public class GetTenantIngestionDataServiceResponse : ResultFunctionResponse<Dictionary<string, TenantIngestionData>>
    {
        /// <summary>
        /// Success Constructor.
        /// </summary>
        /// <param name="result">Result object.</param>
        /// <param name="successMessage">Success message.</param>
        public GetTenantIngestionDataServiceResponse(Dictionary<string, TenantIngestionData> result, string successMessage)
            : base(result, successMessage)
        {
        }

        /// <summary>
        /// Error Constructor.
        /// </summary>
        public GetTenantIngestionDataServiceResponse(bool hasErrors, string errorMessage)
            : base(hasErrors, errorMessage)
        {
        }

        /// <summary>
        /// Error Constructor with result set.
        /// </summary>
        public GetTenantIngestionDataServiceResponse(bool hasErrors, string errorMessage, Dictionary<string, TenantIngestionData> result)
            : base(hasErrors, errorMessage, result)
        {
        }

        /// <summary>
        /// Error constructor using an integer errorCode which maps to the Error Repository dictionary.
        /// </summary>
        /// <param name="errorCode">Integer matching error code found in Error repository.</param>
        public GetTenantIngestionDataServiceResponse(int errorCode)
            : base(errorCode)
        {
        }

        /// <summary>
        /// Error constructor using an integer errorCode which maps to the Error repository and allows for
        /// parameters to be passed when using a dynamic message with tokens.
        /// </summary>
        /// <param name="errorCode">Integer matching error code found in Error repository.</param>
        /// <param name="values">Additional object parameters used to replace tokens in dynamic message.</param>
        public GetTenantIngestionDataServiceResponse(int errorCode, params object[] values)
            : base(errorCode, values)
        {
        }

        /// <summary>
        /// Error constructor when passing in a response object directly to funnel an error down a level.
        /// </summary>
        /// <param name="response">VoidFunctionResponse object</param>
        public GetTenantIngestionDataServiceResponse(VoidFunctionResponse response)
            : base(response)
        {
        }

        /// <summary>
        /// Return the id/message dictionary object (TenantIngestionDataServiceResponse). 
        /// </summary>
        /// <returns></returns>
        protected override BaseErrorMessages SetErrorRepository()
        {
            return new GetTenantIngestionDataServiceResponseErrorDictionary();
        }
    }
}
