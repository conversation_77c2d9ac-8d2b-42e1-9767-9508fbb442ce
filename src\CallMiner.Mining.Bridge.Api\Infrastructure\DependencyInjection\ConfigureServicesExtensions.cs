﻿using CallMiner.Infrastructure.Extensions;
using CallMiner.Infrastructure.Utils;
using CallMiner.Infrastructure.Web.Models;
using CallMiner.Infrastructure.Web.Services;
using CallMiner.IO.Unc;
using CallMiner.Logging;
using CallMiner.ServiceConfiguration.Manager;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.PlatformAbstractions;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Collections.Generic;
using System.IO;
using System.Text.Json.Serialization;

namespace CallMiner.Mining.Bridge.Api.Infrastructure.DependencyInjection
{
    /// <summary>
    /// Service Collection for Configuring services
    /// </summary>
    public static class ConfigureServicesExtensions
    {
        private const string _SWAGGER_TITLE = "CallMiner Mining Bridge API";

        /// <summary>
        /// Configures Service for the application
        /// </summary>
        /// <param name="services"></param>
        /// <param name="logProvider"></param>
        /// <returns></returns>
        public static IServiceCollection ConfigureServices(this IServiceCollection services, ILogProvider logProvider)
        {
            // Register configuration objects.
            services.RegisterConfigurationOptions();

            services.AddSingletonServices(logProvider);
            
            services.AddTransientServices(logProvider);

            // UNC services
            services.AddUncServices(ServiceConfigurationManager.Configuration);

            // Framework Required Services
            services.AddDefaultConfiguration(options =>
            {
                options.Configuration = ServiceConfigurationManager.Configuration;
                options.ServiceName = ServiceConfigurationManager.ServiceName;
                options.DisableDefaultAuthentication = true;
                options.DisableDefaultAuthorization = true;
                options.DisableDefaultSwagger = true;
                options.SwaggerTitle = _SWAGGER_TITLE;
                options.OptionalFilter1 = ServiceConfigurationManager.OptionalFilter1;
                options.OptionalFilter2 = ServiceConfigurationManager.OptionalFilter2;
                options.ShouldAuditLog = false;
                options.LogProvider = ServiceConfigurationManager.LogProvider;
            });

            // custom Swagger without auth
            services.AddSwaggerGen(config =>
            {
                config.SwaggerDoc("v1", new OpenApiInfo { Title = _SWAGGER_TITLE, Version = "v1" });

                config.DescribeAllEnumsAsStrings();
                config.DescribeStringEnumsInCamelCase();

                // Set the comments path for the Swagger JSON and UI.
                var basePath = PlatformServices.Default.Application.ApplicationBasePath;
                foreach (var xmlPath in FileUtils.GetAllXmlFilesInSolution())
                {
                    if (File.Exists(xmlPath))
                    {
                        config.IncludeXmlComments(Path.Combine(basePath, xmlPath), true);
                    }
                }
                // hide CustomSwaggerTabs
                config.DocumentFilter<HideInDocsFilter>(false);
            });

            services.AddSwaggerGenNewtonsoftSupport();
            services.AddSwaggerExamples();

            // EA-6060: Because this API doesn't use the standard Swagger from in CallMiner.Infrastructure
            // (not sure why, there isn't anything being done here that infrastructure doesn't support, as far as I can see),
            // a DI exception was being thrown because the custom swagger tab service was never registered for DI.
            // So, as a quick fix, we add this line here to register it and make the exception go away.
            // There may be a more reusable fix that can be done in the infrastructure library, if we have time later.
            services.AddCustomSwaggerTabs(new List<CustomSwaggerTab>(), false);

            // add custom json serialization.
            services.AddControllers()
                .SetCompatibilityVersion(Microsoft.AspNetCore.Mvc.CompatibilityVersion.Version_3_0)
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
                })
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.PreserveReferencesHandling = PreserveReferencesHandling.None;
                    options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Serialize;
                    options.SerializerSettings.ContractResolver = new DefaultContractResolver();
                    // fixed enum casing in Swagger
                    options.SerializerSettings.Converters.Add(new CallMinerStringEnumConverter());
                });

            return services;
        }
    }
}
