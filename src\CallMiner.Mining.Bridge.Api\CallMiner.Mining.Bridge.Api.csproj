﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>BridgeRestApi</AssemblyName>
  </PropertyGroup>

  <PropertyGroup>
    <DocumentationFile>bin\$(Configuration)\netcoreapp3.1\$(AssemblyName).xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CallMiner.ServiceConfiguration.Manager" Version="2024.5.1.1" />
    <PackageReference Include="CallMiner.Infrastructure.Repositories" Version="2024.10.11.1" />
    <PackageReference Include="CallMiner.Infrastructure.Web" Version="2024.10.11.1" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting.WindowsServices" Version="3.1.3" />    
    <PackageReference Include="CallMiner.NgmFile" Version="2023.3.1.2" />
    <PackageReference Include="CallMiner.Validation.FluentValidation" Version="2023.4.21.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CallMiner.Mining.Bridge.Api.Domain\CallMiner.Mining.Bridge.Api.Domain.csproj" />
    <ProjectReference Include="..\CallMiner.Mining.Bridge.Api.Services\CallMiner.Mining.Bridge.Api.Services.csproj" />
  </ItemGroup>
</Project>
