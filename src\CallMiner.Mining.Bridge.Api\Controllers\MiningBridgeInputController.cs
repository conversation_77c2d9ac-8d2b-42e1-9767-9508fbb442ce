using CallMiner.Mining.Bridge.Api.Domain;
using CallMiner.Mining.Bridge.Api.Services;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Filters;
using CallMiner.Mining.Bridge.Api.Infrastructure;

namespace CallMiner.Mining.Bridge.Api.Controllers
{
	/// <summary>
	/// Mining Bridge input controller.
	/// </summary>
    [Route("api/ingestion")]
    [Produces("application/json")]
    public class MiningBridgeInputController : Controller
	{
		private readonly IMiningBridgeSingleFileInputService _miningBridgeSingleFileInputService;

		/// <summary>
		/// Initializes a new instance of the MiningBridgeInputController class.
		/// </summary>
		/// <param name="miningBridgeSingleFileInputService">Service used to process a mining bridge single file input request.</param>
		public MiningBridgeInputController(IMiningBridgeSingleFileInputService miningBridgeSingleFileInputService)
		{
			_miningBridgeSingleFileInputService = miningBridgeSingleFileInputService;
		}

        /// <summary>
        /// Inserts a file into the mining system to start the metadata and audio pairing process.
        /// </summary>
        /// <remarks>
        /// Inserts a file into the mining system to start the metadata and audio pairing process for Tenants configured with an FTP account.
        /// </remarks>
        /// <param name="request">Request object with the mining contact information.</param>
        /// <returns>The Correlation Id.</returns>
        /// <response code="200">The job id.</response>
        /// <response code="400">Error object with an error message.</response>
        [Route("unpaired-content-metadata"), HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [SwaggerRequestExample(typeof(MiningBridgeSingleFileInputRequest), typeof(MiningBridgeSingleFileInputRequestExample))]
		public ActionResult<string> InsertUnpairedContentMetadataFile([ModelBinder(typeof(BodyFormModelBinder))]MiningBridgeSingleFileInputRequest request)
		{
			// Process the request.
			var response = _miningBridgeSingleFileInputService.Process(request);

			if (response.HasErrors)
			{
				// Return 400.
				return BadRequest(response.FormattedErrorString);
			}

			// Return 200.
			return Ok(response.Result);
		}
    }
}
