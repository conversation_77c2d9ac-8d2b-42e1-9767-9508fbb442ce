﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>
  
  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="CallMiner.IO.Unc" Version="2023.7.12.1" />
    <PackageReference Include="CallMiner.NgmFile" Version="2023.3.1.2" />
    <PackageReference Include="CallMiner.QueueMessagePublisher.RabbitMQ" Version="2024.6.14.1" />
    <PackageReference Include="CallMiner.Runtime.Caching" Version="2022.6.9.2" />
    <PackageReference Include="CallMiner.ServiceFramework.Domain" Version="2024.10.11.1" />
    <PackageReference Include="CallMiner.SystemMetadata.SDK.Http.Core" Version="2024.7.23.1" />
    <PackageReference Include="CallMiner.Visibility.Input.Domain" Version="2023.2.14.1" />
    <PackageReference Include="CallMiner.Visibility.SDK" Version="2024.7.17.1" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\CallMiner.Mining.Bridge.Api.Repositories\CallMiner.Mining.Bridge.Api.Repositories.csproj" />
  </ItemGroup>
</Project>
