﻿using System;
using System.Collections.Generic;
using System.Text;

namespace CallMiner.Mining.Bridge.Api.Configuration
{
    public class BridgeFilteringConfiguration
    {

        /// <summary>
        /// Defines a list of valid metadata file extensions.
        /// </summary>
        public HashSet<string> MetadataFileExtensions { get; set; } = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// Defines a list of valid audio file extensions.
        /// </summary>
        public HashSet<string> AudioFileExtensions { get; set; } = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

    }
}
