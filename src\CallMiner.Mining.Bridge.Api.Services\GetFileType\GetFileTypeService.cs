﻿using CallMiner.Mining.Bridge.Api.Domain;
using CallMiner.ServiceConfiguration.Sections.BridgeFiltering;
using System;
using System.IO;
using System.Linq;

namespace CallMiner.Mining.Bridge.Api.Services
{

    /// <summary>
    /// Resposible for determining if the file is of type Metadata or Audio.
    /// </summary>
    public class GetFileTypeService : IGetFileTypeService
    {

        private readonly BridgeFilteringSection _bridgeFilteringConfiguration;

        /// <summary>
        /// Default constructor.
        /// </summary>
        /// <param name="bridgeFilteringConfiguration">An instance of the BridgeFilteringConfiguration object containing the filtering configuration.</param>
        public GetFileTypeService(BridgeFilteringSection bridgeFilteringConfiguration)
        {
            _bridgeFilteringConfiguration = bridgeFilteringConfiguration;
        }


        /// <summary>
        /// Returns the file type of a given file.
        /// </summary>
        /// <param name="fileName">The full path to the file being evaluated.</param>
        /// <returns>A FileType indicating if the file is a metadata file, an audio file or an unknown file type.</returns>
        public FileType GetFileType(string fileName)
        {

            // strip off the pgp extension if it's there.
            if (fileName.EndsWith(".pgp", StringComparison.OrdinalIgnoreCase))
            {
                fileName = Path.GetFileNameWithoutExtension(fileName);
            }

            string extension = Path.GetExtension(fileName);

            string extensionNoPeriod = extension.Trim(new char[] { '.' });

            if (_bridgeFilteringConfiguration.MetadataFileExtensions.Contains(extension) || _bridgeFilteringConfiguration.MetadataFileExtensions.Contains(extensionNoPeriod))
            {
                return FileType.Metadata;
            }
            else if (_bridgeFilteringConfiguration.AudioFileExtensions.Contains(extension) || _bridgeFilteringConfiguration.AudioFileExtensions.Contains(extensionNoPeriod))
            {
                return FileType.Audio;
            }

            // if this hits then the extension was not found in either the audio or metadata extensions so it is an unknown extension.
            return FileType.Unknown;
        }
    }
}
