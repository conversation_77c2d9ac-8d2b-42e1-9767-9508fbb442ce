﻿using CallMiner.ServiceFramework.Domain;
using CallMiner.Visibility.SDK.Services;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CallMiner.SystemMetadata.Domain;
using CallMiner.Visibility.DTO.BatchRequest;
using System.Collections.Generic;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Interface to Visibility service
    /// </summary>
    public class VisibilityRequestService : IVisibilityRequestService
    {
        private readonly IVisibilitySDKService _visibilitySDKService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="visibilitySDKService">Visibility Service SDK</param>
        public VisibilityRequestService(IVisibilitySDKService visibilitySDKService)
        {
            _visibilitySDKService = visibilitySDKService;
        }

        /// <summary>
        /// Performs tracking of request/content in Visibility
        /// </summary>
        /// <param name="request">Router request</param>
        /// <param name="audioSource">Audio source</param>
        /// <param name="isForceNonMineable">Sets non-mineable for non-whitelisted files</param>
        public void TrackRequest(Request request, AudioSource audioSource, bool isForceNonMineable = false)
        {
            var visibility = new List<BaseBatchRequestItemDto>();
            foreach (var content in request.RequestContent)
            {
                visibility.Add(new BatchContentRequestItemDto()
                {
                    Id = content.VisibilityId,
                    ContentType = content.ContentType,
                    CorrelationId = request.CorrelationId,
                    FileFullPath = content.FullFilePath,
                    Status = Visibility.Input.Domain.ProcessingStatus.Processing,
                    CreateTimestamp = request.CreateTimestamp,
                    SourceId = audioSource.Id
                });
                if (isForceNonMineable)
                {
                    visibility.Add(new BatchContentUpdateRequestDto()
                    {
                        Id = content.VisibilityId,
                        IsMineable = false
                    }); ;
                }
            }

            // write request and associations
            visibility.Add(new BatchRequestCreateRequestItemDto()
            {
                Id = request.RequestId,
                CorrelationId = request.CorrelationId,
                RequestType = request.RequestType,
                RequestSource = request.RequestSource,
                CreateTimestamp = request.CreateTimestamp,
                Extension = request.ContentFilter,
                SourceId = audioSource.Id,
                RequestContent = request.RequestContent.Select(x => x.VisibilityId).ToList()
            });

            if (isForceNonMineable)
            {
                visibility.Add(new BatchRequestUpdateRequestItemDto()
                {
                    Id = request.RequestId,
                    IsMineable = false
                });
            }

            // send the visibility request.
            _visibilitySDKService.BatchService.Create(visibility);
        }

        /// <summary>
        /// Returns MS SQL index friendly guid (time sorted)
        /// </summary>
        public Guid NewCombGuid()
        {
            return _visibilitySDKService.NewCombGuid();
        }
    }
}
