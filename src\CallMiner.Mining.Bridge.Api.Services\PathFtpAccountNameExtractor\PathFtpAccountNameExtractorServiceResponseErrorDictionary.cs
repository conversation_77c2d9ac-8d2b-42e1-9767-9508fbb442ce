﻿using CallMiner;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Represents the possible known errors that can occur inside the PathFtpAccountNameExtractorService.
    /// </summary>
    public class PathFtpAccountNameExtractorServiceResponseErrorDictionary : BaseErrorMessages
    {
        /// <summary>
        /// Initializes a new instance of the PathFtpAccountNameExtractorServiceResponseErrorDictionary which provides localized error messages.
        /// </summary>
        public PathFtpAccountNameExtractorServiceResponseErrorDictionary()
        {
            AddMessage("eng", 101, "An input path must be defined.", "d66eb111-395f-49d7-9e27-cec799cd5ada");
            AddMessage("eng", 102, "A tenant ApiKey and/or audio source name could not be found for the path \"{0}\"", "2b99474d-dc9a-4ea8-b924-a93302922aa2");
        }
    }
}
