﻿using CallMiner;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Represents the response for the MiningBridgeSingleFileInputService.
    /// </summary>
    public class MiningBridgeSingleFileInputServiceResponse : ResultFunctionResponse<string>
    {
        /// <summary>
        /// Success Constructor.
        /// </summary>
        /// <param name="result">Result object.</param>
        /// <param name="successMessage">Success message.</param>
        public MiningBridgeSingleFileInputServiceResponse(string result, string successMessage)
            : base(result, successMessage)
        {
        }

        /// <summary>
        /// Error Constructor.
        /// </summary>
        public MiningBridgeSingleFileInputServiceResponse(bool hasErrors, string errorMessage)
            : base(hasErrors, errorMessage)
        {
        }

        /// <summary>
        /// Error Constructor with result set.
        /// </summary>
        public MiningBridgeSingleFileInputServiceResponse(bool hasErrors, string errorMessage, string result)
            : base(hasErrors, errorMessage, result)
        {
        }

        /// <summary>
        /// Error constructor using an integer errorCode which maps to the Error Repository dictionary.
        /// </summary>
        /// <param name="errorCode">Integer matching error code found in Error repository.</param>
        public MiningBridgeSingleFileInputServiceResponse(int errorCode)
            : base(errorCode)
        {
        }

        /// <summary>
        /// Error constructor using an integer errorCode which maps to the Error repository and allows for
        /// parameters to be passed when using a dynamic message with tokens.
        /// </summary>
        /// <param name="errorCode">Integer matching error code found in Error repository.</param>
        /// <param name="values">Additional object parameters used to replace tokens in dynamic message.</param>
        public MiningBridgeSingleFileInputServiceResponse(int errorCode, params object[] values)
            : base(errorCode, values)
        {
        }

        /// <summary>
        /// Error constructor when passing in a response object directly to funnel an error down a level.
        /// </summary>
        /// <param name="response">VoidFunctionResponse object</param>
        public MiningBridgeSingleFileInputServiceResponse(VoidFunctionResponse response)
            : base(response)
        {
        }

        /// <summary>
        /// Return the id/message dictionary object (MiningBridgeSingleFileInputServiceResponse). 
        /// </summary>
        /// <returns></returns>
        protected override BaseErrorMessages SetErrorRepository()
        {
            return new MiningBridgeSingleFileInputServiceResponseErrorDictionary();
        }
    }
}
