﻿using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.Sqlite;
using System.IO;
using System.Linq;
using CallMiner.EnvironmentContext;
using CallMiner.Locking;
using CallMiner.Logging;
using CallMiner.Mining.Bridge.Api.Domain;
using CallMiner.ServiceConfiguration.Sections.BridgeRecovery;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace CallMiner.Mining.Bridge.Api.Repositories
{
    /// <summary>
    /// Responsible for inserting new records into the SQLite recovery database.
    /// </summary>
    public class RecoveryDbRepository : IRecoveryDbRepository
    {
        private readonly ILockingService _lockingService;
        private ILogProvider _logProvider;
        private string _connectionString;
        private string _remotePath;
        private string _fileName;
        private string _localPath;
        private readonly bool _isDocker;

        public RecoveryDbRepository(IOptions<BridgeRecoverySection> recoveryOptions, ILockingService lockingService, ILogProvider logProvider)
        {
            _lockingService = lockingService;
            _logProvider = logProvider;
            _connectionString = recoveryOptions.Value.RecoveryConnectionString;
            _isDocker = ContextHelper.IsDocker;

            // validate the connection string
            if (string.IsNullOrWhiteSpace(_connectionString))
                throw new ArgumentNullException("Recovery connection string is empty");

            Init();
        }

        /// <summary>
        /// Insert a new request received from the API into the local database instance.
        /// </summary>
        /// <param name="request">The request received from the API that will be serialized into json format.</param>
        public void InsertFileInput(MiningBridgeSingleFileInputRequest request)
        {
            // open a connection to the local db instance
            using (var dbConnection = new SqliteConnection(_connectionString))
            {
                dbConnection.Open();

                // prepare the insert command
                using (var command = dbConnection.CreateCommand())
                {
                    command.CommandType = CommandType.Text;
                    command.CommandText = "INSERT INTO RecoveryRequest (ID, Payload, Timestamp) VALUES (@ID, @Payload, @Timestamp)";
                    command.Parameters.Add(new SqliteParameter("@ID", Guid.NewGuid().ToString()));
                    command.Parameters.Add(new SqliteParameter("@Payload", JsonConvert.SerializeObject(request)));
                    command.Parameters.Add(new SqliteParameter("@Timestamp", DbType.DateTime)
                    {
                        Value = DateTime.UtcNow
                    });

                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// Will return a list of pending requests from the recovery database that need to be posted again to the Bridge API.
        /// If the recovery database does not exist, does nothing.
        /// </summary>
        /// <param name="limit">Maximum number of records returned</param>
        /// <returns>List of request objects</returns>
        /// <summary>
        public List<RecoveryRequest> GetPendingRequests(int limit)
        {
            var result = new List<RecoveryRequest>();

            // open a connection to the local db instance
            using (var dbConnection = new SqliteConnection(_connectionString))
            {
                dbConnection.Open();

                // prepare and execute the select statement
                using (var command = dbConnection.CreateCommand())
                {
                    command.CommandType = CommandType.Text;
                    command.CommandText = $"SELECT Id, Payload FROM RecoveryRequest LIMIT {limit}";
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(new RecoveryRequest()
                            {
                                Id = reader.GetString(0),
                                Payload = reader.GetString(1)
                            });
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Deletes a specific request object from the local recovery database based on the request ID.
        /// This is intended to be used after the request has been successfully resent.
        /// </summary>
        /// <param name="request">The request to delete based on its Id</param>
        public void DeleteRequest(RecoveryRequest request)
        {
            // open a connection to the local db instance
            using (var dbConnection = new SqliteConnection(_connectionString))
            {
                dbConnection.Open();

                // prepare and execute the delete statement to delete 1 row based on the ID column
                using (var command = dbConnection.CreateCommand())
                {
                    command.CommandType = CommandType.Text;
                    command.CommandText = "DELETE FROM RecoveryRequest WHERE Id = @ID";
                    command.Parameters.Add(new SqliteParameter("@ID", request.Id));
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// Purges requests older than maxRequestAge from the recovery database.
        /// If the recovery database does not exist, does nothing.
        /// </summary>
        /// <param name="maxRequestAge"></param>
        public void PurgeOldRequests(TimeSpan maxRequestAge)
        {
            // open a connection to the local db instance
            using (var dbConnection = new SqliteConnection(_connectionString))
            {
                dbConnection.Open();

                using (var command = dbConnection.CreateCommand())
                {
                    command.CommandType = CommandType.Text;
                    command.CommandText = "DELETE FROM RecoveryRequest WHERE Timestamp < @MaxRequestAge";
                    command.Parameters.Add(new SqliteParameter("@MaxRequestAge", DateTime.UtcNow.Add(-maxRequestAge)));
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// Shrinks database
        /// </summary>
        public void Shrink()
        {
            // open a connection to the local db instance
            using (var dbConnection = new SqliteConnection(_connectionString))
            {
                dbConnection.Open();
                using (var command = dbConnection.CreateCommand())
                {
                    command.CommandType = CommandType.Text;
                    command.CommandText = "VACUUM";
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// Save remaining data for Docker
        /// </summary>
        public void FlushAndStop()
        {
            _logProvider.LogInfo("FlushAndStop recovery", null);

            // nothing needs to be done for Windows
            if (!_isDocker)
                return;

            // nothing to save
            if (!GetPendingRequests(1).Any())
                return;

            // close connection pools
            SqliteConnection.ClearAllPools();

            // locked file will look like: /mnt/shared/Development/Recovery/Callminer.Mining.Bridge.Api.RecoveryDB.db1
            var lockedFile = _lockingService.GenerateUniqueLock(Path.Combine(_remotePath, _fileName));
            MoveDatabase(_localPath, lockedFile);
            _lockingService.ReleaseLock(lockedFile);
        }

        /// <summary>
        /// Creates the recovery database file and schema if they do not already exist. 
        /// If they do exist, this method has no effect.
        /// </summary>
        private void Init()
        {
            if (_isDocker)
            {
                var parsedConnectionString = new SqliteConnectionStringBuilder(_connectionString);
                var filePath = parsedConnectionString.DataSource;
                _fileName = Path.GetFileName(filePath);
                _remotePath = Path.GetDirectoryName(filePath);

                // switch connection string so it points to local version
                _localPath = Path.Combine(Path.GetTempPath(), "Recovery", _fileName);
                _connectionString = _connectionString.Replace(filePath, _localPath);

                // create remote if does not exist
                Directory.CreateDirectory(_remotePath);
                var remoteDirectory = new DirectoryInfo(_remotePath);
                foreach (var file in remoteDirectory.EnumerateFiles($"{_fileName}*"))
                {
                    // move to the next, not yet locked file
                    if (!_lockingService.ObtainLock(file.FullName))
                        continue;

                    if (!file.Exists)
                    {
                        // file was moved by another instance
                        _lockingService.ReleaseLock(file.FullName);
                        continue;
                    }

                    // file still exists and locked: move and exit loop
                    MoveDatabase(file.FullName, _localPath);
                    _lockingService.ReleaseLock(file.FullName);
                    break;
                }
            }

            var connectionString = new SqliteConnectionStringBuilder(_connectionString);
            _logProvider.LogInfo("Recovery Database initialization", new { connectionString });

            // We need to create the target directory first, if it doesn't already exist
            (new FileInfo(connectionString.DataSource)).Directory.Create();

            // Create the RecoveryRequest table for the database if it doesn't already exist
            using (var dbConnection = new SqliteConnection(_connectionString))
            {
                dbConnection.Open();
                const string createTableCommand = @"CREATE TABLE IF NOT EXISTS RecoveryRequest(ID varchar(36) NOT NULL, Payload TEXT NOT NULL, Timestamp DATETIME NOT NULL);
CREATE INDEX IF NOT EXISTS IX_RecoveryRequest_Id ON RecoveryRequest(Id);
CREATE INDEX IF NOT EXISTS IX_RecoveryRequest_Timestamp ON RecoveryRequest(Timestamp);";

                using (var command = new SqliteCommand(createTableCommand, dbConnection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// Moves database
        /// </summary>
        public void MoveDatabase(string source, string destination)
        {
            // store the list of remotes names, db, wal, shm
            var destinationDirectory = Path.GetDirectoryName(destination);
            var destinationName = Path.GetFileName(destination);
            Directory.CreateDirectory(destinationDirectory);

            // drop main file as well as WAL/SHM log files
            var sourceDirectory = new DirectoryInfo(Path.GetDirectoryName(source));
            var sourceName = Path.GetFileName(source);
            foreach (var file in sourceDirectory.EnumerateFiles($"{sourceName}*"))
            {
                if (file.Name.EndsWith(".lock"))
                    continue;

                var newName = Path.Combine(destinationDirectory, file.Name.Replace(sourceName, destinationName));
                _logProvider.LogInfo($"Start moving recovery database {file.FullName} to {newName}", null);
                // move file and restore create time (netstandard2.0 does not have MoveTo with overwrite flag)
                file.CopyTo(Path.Combine(destinationDirectory, newName), true);
                File.Delete(file.FullName);
                _logProvider.LogInfo($"Moving recovery database {file.FullName} to {newName} is completed", null);
            }
        }
    }
}
