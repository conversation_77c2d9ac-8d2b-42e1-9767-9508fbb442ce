﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CallMiner.Mining.Bridge.Api.Domain;

namespace CallMiner.Mining.Bridge.Api.Services
{

    /// <summary>
    /// Contract defining a service responsible for determining if the file is a metadata or audio file.
    /// </summary>
    public interface IGetFileTypeService
    {

        /// <summary>
        /// Returns the file type of a given file.
        /// </summary>
        /// <param name="fileName">The full path to the file being evaluated.</param>
        /// <returns>A FileType indicating if the file is a metadata file, an audio file or an unknown file type.</returns>
        FileType GetFileType(string fileName);
    }
}
