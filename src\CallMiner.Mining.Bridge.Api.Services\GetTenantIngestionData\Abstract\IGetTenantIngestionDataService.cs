﻿namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Defines the contract for a IGetTenantIngestionDataService, which is responsible for returning a Dictionary<string, TenantIngestionData> 
    /// for all tenants in the NGM data store.
    /// </summary>
    public interface IGetTenantIngestionDataService
    {

        /// <summary>
        /// Retrieves a Dictionary<string, TenantIngestionData> containing the TenantFtpAccountName as the key and
        /// the TenantIngestionData as the value for all tenants.
        /// </summary>
        /// <returns>Returns all tenant ingestion data for all tenants formatted in a Dictionary.</returns>
        GetTenantIngestionDataServiceResponse GetAll();
    }
}
