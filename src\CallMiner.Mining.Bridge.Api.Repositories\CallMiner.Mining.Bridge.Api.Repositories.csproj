﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CallMiner.Logging" Version="2024.6.24.16" />
    <PackageReference Include="CallMiner.Net.Http" Version="2023.12.28.1" />
    <PackageReference Include="CallMiner.SystemMetadata.Domain" Version="2024.7.15.1" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="3.1.0" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="8.0.7" />
    <PackageReference Include="CallMiner.EnvironmentContext" Version="2024.5.1.1" />
    <PackageReference Include="CallMiner.ServiceConfiguration.Sections" Version="2024.7.12.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CallMiner.Mining.Bridge.Api.Domain\CallMiner.Mining.Bridge.Api.Domain.csproj" />
  </ItemGroup>
</Project>
