﻿using CallMiner.Logging;
using CallMiner.Mining.Bridge.Api.Services;
using CallMiner.NgmFile.Services;
using CallMiner.Security.IdentityProvider.Abstract;
using CallMiner.ServiceConfiguration.Manager;
using CallMiner.SystemMetadata.SDK.Http.Core;
using CallMiner.Visibility.SDK.Services;
using Microsoft.Extensions.DependencyInjection;

namespace CallMiner.Mining.Bridge.Api.Infrastructure.DependencyInjection
{
    /// <summary>
    /// Services Extension
    /// </summary>
    public static class AddTransientServicesExtension
    {
        /// <summary>
        /// Register transient services.
        /// </summary>
        /// <param name="services">Services collection.</param>
        /// <param name="logProvider">LogProvider.</param>
        /// <returns>The <see cref="IServiceCollection"/> so that additional calls can be chained.</returns>
        public static void AddTransientServices(this IServiceCollection services, ILogProvider logProvider)
        {
            //Initialize System Metadata SDK, no retries for APIs
            services.RegisterSystemMetadataSDK(ServiceConfigurationManager.Configuration, false);

            //////////////////////////////////
            //REPOSITORIES
            /////////////////////////////////

            // visibility
            services.AddTransient<IVisibilitySDKService>(c => new VisibilitySDKService(
                ServiceConfigurationManager.Configuration,
                c.GetService<IInternalApiSlidingTokenService>(),
                logProvider
            ));

            // File Writer Service for .ngm file
            services.AddTransient<INgmFileWriterService, NgmFileWriterService>();

            //////////////////////////////////
            //SERVICES
            /////////////////////////////////

            // register the get file type service.
            services.AddTransient<IGetFileTypeService, GetFileTypeService>();

            // register the xml metadata builder service.
            services.AddTransient<IXmlMetadataBuilderService, XmlMetadataBuilderService>();

            // Visibility
            services.AddTransient<IVisibilityRequestService, VisibilityRequestService>();

            // Get Tenant ingestion data service.
            services.AddTransient<IGetTenantIngestionDataService, GetTenantIngestionDataService>();

            // Unc path FTP account name extractor service.
            services.AddTransient<IPathFtpAccountNameExtractorService, UncPathFtpAccountNameExtractorService>();

            // Mining bridge single file input service.
            services.AddTransient<IMiningBridgeSingleFileInputService, MiningBridgeSingleFileInputService>();

            // Failover service
            services.AddTransient<IFailoverService, FailoverService>();
        }
    }
}
