﻿using CallMiner;
using CallMiner.Mining.Bridge.Api.Domain;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Represents the response for the PathFtpAccountNameExtractorService.
    /// </summary>
    public class PathFtpAccountNameExtractorServiceResponse : ResultFunctionResponse<TenantFtpAccountData>
    {
        /// <summary>
        /// Success Constructor.
        /// </summary>
        /// <param name="result">Result object.</param>
        /// <param name="successMessage">Success message.</param>
        public PathFtpAccountNameExtractorServiceResponse(TenantFtpAccountData result, string successMessage)
            : base(result, successMessage)
        {
        }

        /// <summary>
        /// Error Constructor.
        /// </summary>
        public PathFtpAccountNameExtractorServiceResponse(bool hasErrors, string errorMessage)
            : base(hasErrors, errorMessage)
        {
        }

        /// <summary>
        /// Error Constructor with result set.
        /// </summary>
        public PathFtpAccountNameExtractorServiceResponse(bool hasErrors, string errorMessage, TenantFtpAccountData result)
            : base(hasErrors, errorMessage, result)
        {
        }

        /// <summary>
        /// Error constructor using an integer errorCode which maps to the Error Repository dictionary.
        /// </summary>
        /// <param name="errorCode">Integer matching error code found in Error repository.</param>
        public PathFtpAccountNameExtractorServiceResponse(int errorCode)
            : base(errorCode)
        {
        }

        /// <summary>
        /// Error constructor using an integer errorCode which maps to the Error repository and allows for
        /// parameters to be passed when using a dynamic message with tokens.
        /// </summary>
        /// <param name="errorCode">Integer matching error code found in Error repository.</param>
        /// <param name="values">Additional object parameters used to replace tokens in dynamic message.</param>
        public PathFtpAccountNameExtractorServiceResponse(int errorCode, params object[] values)
            : base(errorCode, values)
        {
        }

        /// <summary>
        /// Error constructor when passing in a response object directly to funnel an error down a level.
        /// </summary>
        /// <param name="response">VoidFunctionResponse object</param>
        public PathFtpAccountNameExtractorServiceResponse(VoidFunctionResponse response)
            : base(response)
        {
        }

        /// <summary>
        /// Return the id/message dictionary object (PathFtpAccountNameExtractorServiceResponse). 
        /// </summary>
        /// <returns></returns>
        protected override BaseErrorMessages SetErrorRepository()
        {
            return new PathFtpAccountNameExtractorServiceResponseErrorDictionary();
        }
    }
}
