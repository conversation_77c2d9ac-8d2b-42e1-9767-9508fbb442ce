﻿using Microsoft.AspNetCore.Mvc.ModelBinding;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace CallMiner.Mining.Bridge.Api.Infrastructure
{
    /// <summary>
    /// Temporary workaround for Web API behavior when a single controller method reads data as both FORM and JSON
    /// </summary>
    public class BodyFormModelBinder : IModelBinder
    {
        /// <summary>
        /// Handle both JSON and FORM values - it should be dropped in near future by separating single route into 2
        /// </summary>
        /// <param name="bindingContext"></param>
        /// <returns></returns>
        public async Task BindModelAsync(ModelBindingContext bindingContext)
        {
            var request = bindingContext.ActionContext.HttpContext.Request;

            // Get the type that we're looking to bind
            Type type = bindingContext.ModelType;

            if (request.HasFormContentType && request.Form.Any())
            {
                // treat as form data, create an instance of the model
                var result = Activator.CreateInstance(type);

                // set each field to the model usign reflection
                foreach (var entry in request.Form)
                {
                    // Set the value of the given property on the given instance
                    var piInstance = type.GetProperty(entry.Key);
                    // set only if property is found
                    piInstance?.SetValue(result, entry.Value.FirstOrDefault());
                }

                bindingContext.Result = ModelBindingResult.Success(result);
            }
            else if (request.Query.Any())
            {
                var result = Activator.CreateInstance(type);
                // set each field to the model usign reflection
                foreach (var entry in request.Query)
                {
                    // Set the value of the given property on the given instance
                    var piInstance = type.GetProperty(entry.Key);
                    // set only if property is found
                    if ("OriginDate".Equals(entry.Key, StringComparison.OrdinalIgnoreCase))
                    {
                        var originDate = DateTime.Parse(entry.Value.FirstOrDefault());
                        piInstance?.SetValue(result, originDate);
                    }
                    else
                        piInstance?.SetValue(result, entry.Value.FirstOrDefault());
                }
                bindingContext.Result = ModelBindingResult.Success(result);
            }
            else
            {
                // treat as JSON
                var body = await (new StreamReader(request.Body)).ReadToEndAsync();

                var deserialized = JsonConvert.DeserializeObject(body, type);

                bindingContext.Result = ModelBindingResult.Success(deserialized);
            }
        }
    }
}
