﻿namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Defines the contract for a IPathFtpAccountNameExtractorService, which is responsible for extracting the required tenant details from the path.
    /// </summary>
    public interface IPathFtpAccountNameExtractorService
    {

        /// <summary>
        /// Extracts the FTP details and tenant api key.
        /// </summary>
        /// <param name="inputPath">Path where the file is stored.</param>
        /// <returns>An implementation of the CallMiner ResultFunctionResponse with a TenantFtpAccountData Result containing
        /// the Tenant API Key and the FtpAccountPath.</returns>
        PathFtpAccountNameExtractorServiceResponse Extract(string inputPath);
    }
}
