﻿using CallMiner.Logging;
using CallMiner.Mining.Bridge.Api.Repositories;
using CallMiner.QueueMessagePublisher;
using CallMiner.ServiceConfiguration.Sections.BridgeRecovery;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Responsible for executing the recovery process based on a local database.
    /// </summary>
    public class RecoveryService : IRecoveryService
    {
        private readonly ILogProvider _log;
        private readonly TimeSpan _scanInterval;
        private readonly TimeSpan _maxRequestAge;
        private readonly IRecoveryDbRepository _recoveryDbRepository;
        private readonly IMiningBridgeSingleFileInputService _miningBridgeSingleFileInputService;
        private readonly IMessagePublisher _rabbitMqMessagePublisher;
        private object _lock = new object();
        private Timer _timer;

        /// <summary>
        /// Initializes a new instance of RecoveryService class.
        /// </summary>
        public RecoveryService(IOptions<BridgeRecoverySection> recoveryOptions, IRecoveryDbRepository recoveryDbRepository, 
            IMiningBridgeSingleFileInputService miningBridgeSingleFileInputService, 
            IMessagePublisher rabbitMqMessagePublisher, ILogProvider log)
        {
            _scanInterval = recoveryOptions.Value.RecoveryInterval;
            _maxRequestAge = recoveryOptions.Value.RecoveryPurgeInterval;
            _recoveryDbRepository = recoveryDbRepository;
            _miningBridgeSingleFileInputService = miningBridgeSingleFileInputService;
            _rabbitMqMessagePublisher = rabbitMqMessagePublisher;
            _log = log;
            Init();
        }

        /// <summary>
        /// Save remaining data for Docker
        /// </summary>
        public void FlushAndStop()
        {
            _recoveryDbRepository.FlushAndStop();
        }

        /// <summary>
        /// Start recovery thread
        /// </summary>
        private void Init()
        {
            // kick of first run right away, no reason to wait
            _timer = new Timer(ExecuteLocal, _lock, TimeSpan.FromSeconds(0), _scanInterval);
        }

        /// <summary>
        /// Main service that will re-publish failed requests again into the API
        /// </summary>
        private void ExecuteLocal(object state)
        {
            // one instance a time, skip execution if previous run is not completed
            if (Monitor.TryEnter(state))
            {
                try
                {
                    try
                    {
                        PerformRecovery();
                    }
                    catch (Exception e)
                    {
                        _log.LogException("Error executing service", null, e);
                    }
                }
                finally
                {
                    Monitor.Exit(state);
                }
            }
            else
            {
                _log.LogInfo("Recovery is already running, skipping scheduled run", null);
            }
        }

        private void PerformRecovery()
        {
            int processed = 0;

            _recoveryDbRepository.PurgeOldRequests(_maxRequestAge);

            // get the rabbit status before try to send again the request
            if (_rabbitMqMessagePublisher.IsHealthy())
            {
                // get pending requests, no more than 10000 a time, if more is pending, next recovery run will process it
                var pendingRequests = _recoveryDbRepository.GetPendingRequests(10000);

                if (!pendingRequests.Any())
                    return;

                // log number of events found
                _log.LogInfo($"{pendingRequests.Count} pending requests found.", null);

                foreach (var request in pendingRequests)
                {
                    try
                    {
                        // push the request back to the API
                        _miningBridgeSingleFileInputService.ProcessRecovery(request);

                        // delete record from pending request table
                        _recoveryDbRepository.DeleteRequest(request);

                        processed++;
                    }
                    catch (Exception ex)
                    {
                        _log.LogException("Error with request ", new { RequestId = request.Id }, ex);
                    }
                }

                if (processed > 0)
                {
                    _log.LogInfo($"{processed} requests sent, shrinking database", null);
                    _recoveryDbRepository.Shrink();
                }
            }
            else
            {
                // log rabbit service still unavailable
                _log.LogInfo("Rabbit server is not available.", null);
            }
        }
    }
}
