﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CallMiner.ServiceConfiguration.Manager;
using CallMiner.ServiceConfiguration.Sections.Constants;
using CallMiner.ServiceConfiguration.Sections.Owin;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Hosting.WindowsServices;

namespace CallMiner.Mining.Bridge.Api
{
    /// <summary>
    /// Program
    /// </summary>
    public class Program
    {
        /// <summary>
        /// Main
        /// </summary>
        /// <param name="args"></param>
        public static void Main(string[] args)
        {
            try
            {
                var isService = !(Debugger.IsAttached || args.Contains("--console"));
                var host = BuildWebHost(args.Where(arg => arg != "--console").ToArray(), isService);

                if (isService)
                {
                    host.RunAsService();
                }
                else
                {
                    host.Run();
                }
            }
            catch (Exception ex)
            {
                // Log exception when trying to start the service.
                ServiceConfigurationManager.LogFatalException(ex, ConfigurationServices.Bridge, AppDomain.CurrentDomain.BaseDirectory);
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Create the webhost builder and build
        /// </summary>
        /// <param name="args"></param>
        /// <param name="hostAsService"></param>
        /// <returns></returns>
        public static IWebHost BuildWebHost(string[] args, bool hostAsService = true)
        {
            string pathToContentRoot = null;
            if (hostAsService)
            {
                var pathToExe = Process.GetCurrentProcess().MainModule.FileName;
                pathToContentRoot = Path.GetDirectoryName(pathToExe);
            }

            ServiceConfigurationManager.Initialize(ConfigurationServices.Bridge, pathToContentRoot);

            var owinConfig = ServiceConfigurationManager.Get<WebApiOwinConfigSection>(ConfigurationSections.WebApiOwin);

            var urls = new List<string>();
            foreach (OwinBinding binding in owinConfig.Bindings)
            {
                urls.Add(binding.Address);
            }

            var webhost = WebHost.CreateDefaultBuilder(args)
                             .UseUrls(urls.ToArray())
                             .UseKestrel()
                             .UseStartup<Startup>();

            //we want to set content root when hosting as a service
            if (hostAsService)
            {
                webhost.UseContentRoot(pathToContentRoot);
            }

            return webhost.Build();
        }
    }
}
