﻿using CallMiner.Mining.Bridge.Api.Domain;
using Swashbuckle.AspNetCore.Filters;

namespace CallMiner.Mining.Bridge.Api
{
    /// <summary>
    /// Example MiningBridgeSingleFileInputRequest 
    /// </summary>
    public class MiningBridgeSingleFileInputRequestExample : IExamplesProvider<MiningBridgeSingleFileInputRequest>, ISwaggerExample
    {
        /// <summary>
        /// Return an example of the MiningBridgeSingleFileInputRequest.
        /// </summary>
        /// <returns></returns>
        public MiningBridgeSingleFileInputRequest GetExamples()
        {
            return new MiningBridgeSingleFileInputRequest
            {
                InputFilePath = "ftp.path/file.xml"
            };
        }
    }
}