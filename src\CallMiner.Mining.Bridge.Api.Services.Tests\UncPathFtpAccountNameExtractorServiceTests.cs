﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using CallMiner.SystemMetadata.Domain;
using Moq;
using CallMiner.Runtime.Caching;
using System.Diagnostics.CodeAnalysis;
using CallMiner.Logging;
using CallMiner.SystemMetadata.SDK;
using System.IO;

namespace CallMiner.Mining.Bridge.Api.Services.Tests
{
    [ExcludeFromCodeCoverage]
    [TestClass]
    public class UncPathFtpAccountNameExtractorServiceTests
    {
        private List<TenantIngestionData> MockTenantIngestionData()
        {
            return new List<TenantIngestionData>(){

                new TenantIngestionData(){
                    AudioSourceName = "audiosource",
                    TenantApiKey = "apikey",
                    IngestionSource = new FtpIngestionSource()
                    {
                        IgnoreAudioSourceName = false,
                        Type = IngestionSourceType.FtpIngestionSource,
                        SourceDirectoryName = new DirectoryName
                        {
                            Type = DirectoryNameType.FtpAccountRootDirectory,
                            Id = Guid.NewGuid(),
                            DirectoryPath = null,
                            Name = "ftp.account"
                        }
                    }
                },
                new TenantIngestionData(){
                    AudioSourceName = "audiosource",
                    TenantApiKey = "apikeya",
                    IngestionSource = null
                },
                new TenantIngestionData(){
                    AudioSourceName = "audiosource",
                    TenantApiKey = "apikeyb",
                    IngestionSource = new FtpIngestionSource()
                    {
                        IgnoreAudioSourceName = true,
                        Type = IngestionSourceType.MultipleFileFive9Ftp,
                        SourceDirectoryName = new DirectoryName
                        {
                            Type = DirectoryNameType.FtpAccountRootDirectory,
                            Id = Guid.NewGuid(),
                            DirectoryPath = null,
                            Name = "ftp.account"
                        }
                    }
                },
                new TenantIngestionData(){
                    AudioSourceName = "audiosource",
                    TenantApiKey = "apikeyc",
                    IngestionSource = new FtpIngestionSource()
                    {
                        IgnoreAudioSourceName = false,
                        Type = IngestionSourceType.FtpIngestionSource,
                        SourceDirectoryName = new DirectoryName
                        {
                            Type = DirectoryNameType.FtpAccountRootDirectory,
                            Id = Guid.NewGuid(),
                            DirectoryPath = null,
                            Name = Path.Combine("ftp.account", "c")
                        }
                    }
                },
                new TenantIngestionData(){
                    AudioSourceName = "audiosource",
                    TenantApiKey = "apikeyd",
                    IngestionSource = new FtpIngestionSource()
                    {
                        IgnoreAudioSourceName = false,
                        Type = IngestionSourceType.FtpIngestionSource,
                        SourceDirectoryName = new DirectoryName
                        {
                            Type = DirectoryNameType.FtpAccountRootDirectory,
                            Id = Guid.NewGuid(),
                            DirectoryPath = null,
                            Name = Path.Combine("ftp.account", "d")
                        }
                    }
                },
                new TenantIngestionData(){
                    AudioSourceName = "audiosource",
                    TenantApiKey = "tenant_english",
                    IngestionSource = new FtpIngestionSource()
                    {
                        IgnoreAudioSourceName = false,
                        Type = IngestionSourceType.FtpIngestionSource,
                        SourceDirectoryName = new DirectoryName
                        {
                            Type = DirectoryNameType.FtpAccountRootDirectory,
                            Id = Guid.NewGuid(),
                            DirectoryPath = null,
                            Name = Path.Combine("ftp.tenant_english", "en")
                        }
                    }
                },
                new TenantIngestionData(){
                    AudioSourceName = "audiosource",
                    TenantApiKey = "tenant_spanish",
                    IngestionSource = new FtpIngestionSource()
                    {
                        IgnoreAudioSourceName = false,
                        Type = IngestionSourceType.FtpIngestionSource,
                        SourceDirectoryName = new DirectoryName
                        {
                            Type = DirectoryNameType.FtpAccountRootDirectory,
                            Id = Guid.NewGuid(),
                            DirectoryPath = null,
                            Name = Path.Combine("ftp.tenant_spanish","sp")
                        }
                    }
                }


            };
        }

        [TestMethod]
        public void UncPathFtpAccountNameExtractorServiceTest_Extract_ShouldFindAudioSource()
        {
            var uncExtractor = new UncPathFtpAccountNameExtractorService(GetTenantIngestionDataService());
            var result = uncExtractor.Extract(Path.Combine("test","ftp.account","audiosource","file.wav"));
            Assert.AreEqual(result.Result.TenantApiKey, "apikey");
            Assert.AreEqual(result.Result.AudioSourceName, "audiosource");
        }

        [TestMethod]
        public void UncPathFtpAccountNameExtractorServiceTest_Extract_ShouldFindTenantWithAnyRoot()
        {
            var uncExtractor = new UncPathFtpAccountNameExtractorService(GetTenantIngestionDataService());
            var result = uncExtractor.Extract(Path.Combine("test","a","b","c","d","ftp.account", $"audiosource{Path.DirectorySeparatorChar}"));
            Assert.AreEqual(result.Result.TenantApiKey, "apikey");
            Assert.AreEqual(result.Result.AudioSourceName, "audiosource");
        }

        [TestMethod]
        public void UncPathFtpAccountNameExtractorServiceTest_Extract_ShouldFindRootPathedAudioSource()
        {
            var uncExtractor = new UncPathFtpAccountNameExtractorService(GetTenantIngestionDataService());
            var result = uncExtractor.Extract(Path.Combine("test","ftp.account","file.wav"));
            Assert.AreEqual(result.Result.TenantApiKey, "apikeyb");
            Assert.AreEqual(result.Result.AudioSourceName, "audiosource");
        }

        [TestMethod]
        public void UncPathFtpAccountNameExtractorServiceTest_Extract_ShouldTenantWhenDirectoryNameIsAPath()
        {
            var uncExtractor = new UncPathFtpAccountNameExtractorService(GetTenantIngestionDataService());
            var result = uncExtractor.Extract(Path.Combine("test","a","b","c","d","ftp.tenant_english","en","audiosource","a.wav"));
            Assert.AreEqual(result.Result.TenantApiKey, "tenant_english");
            Assert.AreEqual(result.Result.AudioSourceName, @"audiosource");
        }

        [TestMethod]
        public void UncPathFtpAccountNameExtractorServiceTest_Extract_ShouldFailWithError102_NoTenantFound()
        {
            var uncExtractor = new UncPathFtpAccountNameExtractorService(GetTenantIngestionDataService());
            var result = uncExtractor.Extract(Path.Combine("test","ftp.accountdoesntexist","f","m","g"));
            Assert.IsTrue(result.HasErrors);
            Assert.AreEqual(result.ErrorCode, 102);
        }

        [TestMethod]
        public void UncPathFtpAccountNameExtractorServiceTest_Extract_ShouldFailWithError101_NoInputPathProvided()
        {
            var uncExtractor = new UncPathFtpAccountNameExtractorService(GetTenantIngestionDataService());
            var result = uncExtractor.Extract(null);
            Assert.IsTrue(result.HasErrors);
            Assert.AreEqual(result.ErrorCode, 101);
        }

        private ICacheProvider GenericCacheProvider()
        {
            var mockCache = new Mock<ICacheProvider>();
            mockCache.Setup(m => m.Get(It.IsAny<string>())).Returns(null);
            mockCache.Setup(m => m.Add(It.IsAny<string>(), It.IsAny<object>()));
            return mockCache.Object;
        }

        private ITenantIngestionService GetRestRepository()
        {
            var result = new Mock<ITenantIngestionService>();
            result.Setup(m => m.GetAllTenantsIngestions()).Returns(MockTenantIngestionData());
            return result.Object;
        }

        private IGetTenantIngestionDataService GetTenantIngestionDataService()
        {
            var logger = new Mock<ILogProvider>();
            logger.Setup(m => m.LogException(It.IsAny<string>(), It.IsAny<Object>(), It.IsAny<Exception>()));

            var result = new GetTenantIngestionDataService(GetRestRepository(), GenericCacheProvider(), logger.Object);
            return result;
        }
    }
}