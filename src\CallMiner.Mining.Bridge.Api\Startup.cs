﻿using CallMiner.Infrastructure.Extensions;
using CallMiner.Infrastructure.Web.Models;
using CallMiner.Logging;
using CallMiner.Mining.Bridge.Api.Services;
using CallMiner.ServiceConfiguration.Manager;
using CallMiner.Mining.Bridge.Api.Infrastructure.DependencyInjection;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System;
using CallMiner.Infrastructure.Web.Services;

namespace CallMiner.Mining.Bridge.Api
{
    /// <summary>
    /// Start up the API
    /// </summary>
    public class Startup
    {
        private ILogProvider _log = null;
        private IRecoveryService _recoveryService;

        /// <summary>
        /// Configuration settings
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// Default Constructor
        /// </summary>
        /// <param name="configuration"></param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// This method gets called by the runtime. Use this method to add services to the container.
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            // global logger
            _log = ServiceConfigurationManager.LogProvider;

            try
            {
                //Intialize LogProvider
                services.AddSingleton(_log);

                _log.LogInfo("Attempting to start service.", null);

                services.ConfigureServices(_log);

                _log.LogInfo("Service started successfully.", null);
            }
            catch (Exception ex)
            {
                _log.LogException("Unexpected error occurred in service onstart.", null, ex);
                _log.CloseAndFlush();
                throw;
            }
        }

        /// <summary>
        /// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="applicationLifetime"></param>
        /// <param name="appSettings"></param>
        /// <param name="recoveryService"></param>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHostApplicationLifetime applicationLifetime, IOptions<ApplicationBuilderSettings> appSettings,
            IRecoveryService recoveryService)
        {
            app.UseRouting();

            // register shutdown event
            applicationLifetime.ApplicationStopped.Register(OnShutdown);

            // Ensure that the recovery database gets created at app start, so we can fail early if there is a problem
            app.ApplicationServices.GetRequiredService<IFailoverService>();

            // register recovery for graceful shutdown handling
            _recoveryService = recoveryService;

            app.UseDefaultApiConfig(appSettings.Value);

            // custom swagger without auth
            app.UseSwagger();

            app.UseStaticFiles(DefaultSwaggerConfigurationService.GetStaticFileOptions());

            app.UseSwaggerUI(c =>
            {
                c.DocumentTitle = appSettings.Value.SwaggerTitle;
                c.DefaultModelsExpandDepth(0);
                c.SwaggerEndpoint("../swagger/v1/swagger.json", appSettings.Value.SwaggerTitle);
                c.IndexStream = () => DefaultSwaggerConfigurationService.GetIndexStream();
            });

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseHsts();
                app.UseHttpsRedirection();
            }
        }

        private void OnShutdown()
        {
            _recoveryService?.FlushAndStop();
            _log?.LogInfo("Service stopped successfully.", null);
            _log?.CloseAndFlush();
        }
    }
}
