﻿using CallMiner.Infrastructure.Web;
using CallMiner.Infrastructure.Web.Abstract;
using CallMiner.Infrastructure.Web.Services;
using CallMiner.Locking;
using CallMiner.Logging;
using CallMiner.Mining.Bridge.Api.Repositories;
using CallMiner.Mining.Bridge.Api.Services;
using CallMiner.QueueMessagePublisher;
using CallMiner.QueueMessagePublisher.RabbitMQ;
using CallMiner.Runtime.Caching;
using CallMiner.ServiceConfiguration.Manager;
using CallMiner.ServiceConfiguration.Sections.BridgeFiltering;
using CallMiner.ServiceConfiguration.Sections.Constants;
using CallMiner.ServiceConfiguration.Sections.RabbitMQ;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CallMiner.Mining.Bridge.Api.Infrastructure.DependencyInjection
{
    /// <summary>
    /// Singleton Services
    /// </summary>
    public static class AddSingletonServicesExtension
    {
        /// <summary>
        /// Register transient services.
        /// </summary>
        /// <param name="services">Services collection.</param>
        /// <param name="logProvider">LogProvider.</param>
        /// <returns>The <see cref="IServiceCollection"/> so that additional calls can be chained.</returns>
        public static void AddSingletonServices(this IServiceCollection services, ILogProvider logProvider)
        {
            ///////////////////////////////////////
            //CONFIGURATION SECTIONS
            ///////////////////////////////////////

            var bridgeFilteringConfiguration = ServiceConfigurationManager.Get<BridgeFilteringSection>(ConfigurationSections.BridgeFiltering);

            services.AddSingleton(bridgeFilteringConfiguration);

            var serverSetting = ServiceConfigurationManager.Get<ServerSettings>(ConfigurationSections.RabbitMQPublishApiServerSettings);

            var queueName = ServiceConfigurationManager.Get<QueueName>(ConfigurationSections.QueueName);

            var rabbitMqConnectionSettings = new RabbitMQConnectionSettings(
                serverSetting.HostName,
                serverSetting.Authentication.Username,
                serverSetting.Authentication.Password,
                serverSetting.ExchangeName,
                queueName.ChannelName,
                serverSetting.UseTLS,
                serverSetting.Port).ConfigureTLS(ServiceConfigurationManager.Configuration, logProvider);

            services.AddSingleton(rabbitMqConnectionSettings);

            // multi-channel Rabbit options
            int poolSize = ServiceConfigurationManager.Get<PublishChannelPool>(ConfigurationSections.RabbitMQPublishChannelPool).Size;

            // create instance here to avoid Unity crashes on delayed initialization when Rabbit is down
            var publisher = new RabbitMQPooledMessagePublisher(rabbitMqConnectionSettings,
                    poolSize,
                    ServiceConfigurationManager.ServiceName,
                    logProvider
            );

            // Rabbit message publisher service
            services.AddSingleton<IMessagePublisher>(publisher);

            // Main Recovery Service
            services.AddSingleton<IRecoveryService, RecoveryService>();

            // Mining bridge file input repository for Recovery
            services.AddSingleton<IRecoveryDbRepository, RecoveryDbRepository>();

            services.AddSingleton<ILockingService, LockingService>();

            // Memory cache provider.
            services.AddSingleton<ICacheProvider>(c => new InMemoryCacheProvider(TimeSpan.FromMinutes(15)));

            // healthcheck
            services.AddSingleton(s => new List<IHealthCheckService>
            {
                new RabbitConnectHealthCheckService(new IMessagePublisher[] { publisher }, logProvider, false)
            }.AsEnumerable());

            services.AddSingleton<IHealthCheckManager, HealthCheckManager>();
        }
    }
}
