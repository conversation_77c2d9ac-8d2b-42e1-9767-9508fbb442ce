﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34221.43
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CallMiner.Mining.Bridge.Api", "src\CallMiner.Mining.Bridge.Api\CallMiner.Mining.Bridge.Api.csproj", "{C9C29CC9-7825-4E5A-9206-B72CE875FA60}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CallMiner.Mining.Bridge.Api.Domain", "src\CallMiner.Mining.Bridge.Api.Domain\CallMiner.Mining.Bridge.Api.Domain.csproj", "{8158EBDC-173A-431E-9C65-4940F87AA197}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CallMiner.Mining.Bridge.Api.Services", "src\CallMiner.Mining.Bridge.Api.Services\CallMiner.Mining.Bridge.Api.Services.csproj", "{8019CC2C-7C0F-480B-849F-AEAF57877840}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CallMiner.Mining.Bridge.Api.Repositories", "src\CallMiner.Mining.Bridge.Api.Repositories\CallMiner.Mining.Bridge.Api.Repositories.csproj", "{434916A8-19E9-4FC5-BBD2-D8EB0E496221}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CallMiner.Mining.Bridge.Api.Services.Tests", "src\CallMiner.Mining.Bridge.Api.Services.Tests\CallMiner.Mining.Bridge.Api.Services.Tests.csproj", "{2E708E6D-2D3A-4A53-A13B-639D68F09288}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C9C29CC9-7825-4E5A-9206-B72CE875FA60}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C9C29CC9-7825-4E5A-9206-B72CE875FA60}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C9C29CC9-7825-4E5A-9206-B72CE875FA60}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C9C29CC9-7825-4E5A-9206-B72CE875FA60}.Release|Any CPU.Build.0 = Release|Any CPU
		{8158EBDC-173A-431E-9C65-4940F87AA197}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8158EBDC-173A-431E-9C65-4940F87AA197}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8158EBDC-173A-431E-9C65-4940F87AA197}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8158EBDC-173A-431E-9C65-4940F87AA197}.Release|Any CPU.Build.0 = Release|Any CPU
		{8019CC2C-7C0F-480B-849F-AEAF57877840}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8019CC2C-7C0F-480B-849F-AEAF57877840}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8019CC2C-7C0F-480B-849F-AEAF57877840}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8019CC2C-7C0F-480B-849F-AEAF57877840}.Release|Any CPU.Build.0 = Release|Any CPU
		{434916A8-19E9-4FC5-BBD2-D8EB0E496221}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{434916A8-19E9-4FC5-BBD2-D8EB0E496221}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{434916A8-19E9-4FC5-BBD2-D8EB0E496221}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{434916A8-19E9-4FC5-BBD2-D8EB0E496221}.Release|Any CPU.Build.0 = Release|Any CPU
		{2E708E6D-2D3A-4A53-A13B-639D68F09288}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2E708E6D-2D3A-4A53-A13B-639D68F09288}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2E708E6D-2D3A-4A53-A13B-639D68F09288}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2E708E6D-2D3A-4A53-A13B-639D68F09288}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3AD002D1-5FDF-4876-8E00-66715D1936FB}
	EndGlobalSection
EndGlobal
