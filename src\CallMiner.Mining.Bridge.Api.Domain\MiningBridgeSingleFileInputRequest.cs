﻿using System;

namespace CallMiner.Mining.Bridge.Api.Domain
{
    /// <summary>
    /// Represents a bridge single file input request.
    /// </summary>
    public class MiningBridgeSingleFileInputRequest
    {
        /// <summary>
        /// Gets or sets the full path of the input file.
        /// </summary>
        public string InputFilePath { get; set; } = string.Empty;

        /// <summary>
        /// OriginDate is the date time of the request.
        /// </summary>
        public DateTime OriginDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Gets or sets the Tenant api key.
        /// </summary>
        public string TenantApiKey { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the audio source name.
        /// </summary>
        public string AudioSourceName { get; set; } = string.Empty;
    }
}