resources:
  repositories:
  - repository: BridgeAPI
    type: git
    name: NGM/BridgeAPI
    ref: main
  - repository: Kubernetes
    type: git
    name: Kubernetes/Kubernetes
    ref: main
  - repository: Templates
    type: git
    name: ReleaseMgt/Templates
    ref: main

name: $(releaseVersion)$(Rev:.r)

variables:
 - template: UNI_AZDO_GlobalVariables.yml@Templates

 - group: Development_Repo_Sync_Variables

 - group: Container_Variables
 - group: Deployment_Variables
 - group: Release_Variables
 - name: BuildConfiguration
   value: "release"
 - name: BuildPlatform
   value: "anycpu"
 - name: BuildTypeConfiguration
   value: "API-Int"
 - name: ComponentName
   value: "bridgeapi"
 - name: OctopusProject
   value: "BridgeAPI"
 - name: VersionFile
   value: $(Build.BinariesDirectory)/$(ComponentName).version
 - name: OctopusProjectWin
   value: "NGM_API_BridgeRestAPI"

stages:
- stage: "WindowsBuild"
  displayName: Windows Build
  jobs:
    - job: "WindowsJob"
      displayName: Windows Job
      pool:
        name: Default
      steps:
      - checkout: BridgeAPI
      - script: dir $(Build.SourcesDirectory)

      - template: UNI_JIRA_ValidateTicketOnCommit.yml@Templates
      - template: UNI_AZDO_GenerateDeploymentVariables.yml@Templates
      - template: WIN_AZDO_SetAssemblyManifestData.yml@Templates
      - template: UNI_NET_DotNetPublish.yml@Templates
      - template: UNI_AZDO_CreateVersionFile.yml@Templates
      - task: PowerShell@2
        displayName: 'OVERRIDE - BridgeRestAPI'
        inputs:
         targetType: inline
         script: |
            $packagename = "BridgeRestAPI"
            Write-Host "##vso[task.setvariable variable=PackageName;]$packagename"
            Write-Host "OVERRIDE - Your NEW PackageName value will be set to - " $packagename
      - template: UNI_OCTO_PackageAndPushTo.yml@Templates
      - template: UNI_OCTO_CreateAndDeployRelease.yml@Templates

- stage: "DockerBuild"
  displayName: 'Docker Build'
  dependsOn: []
  jobs:
    - job: "BuildJob"
      displayName: Build Job
      pool:
        name: Linux
      steps:
      - checkout: BridgeAPI
      - checkout: Kubernetes
      - script: dir $(Build.SourcesDirectory)

      - template: UNI_AZDO_GenerateDeploymentVariables.yml@Templates
      - template: LIN_AZDO_SetAssemblyManifestData.yml@Templates
      - template: LIN_NET_DotNetPublish.yml@Templates
      - template: UNI_AZDO_CreateVersionFile.yml@Templates
      - template: LIN_DOCK_CreateVersionFile.yml@Templates
      - template: LIN_DOCK_BuildAndPushFile.yml@Templates
      - template: LIN_DOCK_BuildAndPushFileDevelopmentSync.yaml@Templates

      - template: LIN_DOCK_BuildK8sDeploymentYaml.yml@Templates
      - template: LIN_OCTO_PackageAndPushTo.yml@Templates
      #- template: LIN_OCTO_CreateRelease.yml@Templates

