﻿using FluentValidation;
using FluentValidation.Results;
using CallMiner.Validation.FluentValidation;
using CallMiner.ServiceConfiguration.Sections.BridgeRecovery;

namespace CallMiner.Mining.Bridge.Api.Configuration
{
    /// <summary>
    /// Responsible for validating a <see cref="BridgeRecoverySection"/> instance.
    /// </summary>
    public class BridgeRecoverySectionValidator : AbstractValidator<BridgeRecoverySection>
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public BridgeRecoverySectionValidator()
        {
            RuleFor(i => i.RecoveryConnectionString).NotEmpty().WithMessage(ErrorMessages.NotEmpty);
            RuleFor(i => i.RecoveryInterval).NotEmpty().WithMessage(ErrorMessages.NotEmpty);
            RuleFor(i => i.RecoveryPurgeInterval).NotEmpty().WithMessage(ErrorMessages.NotEmpty);
        }

        /// <summary>
        /// PreValidate override.
        /// </summary>
        protected override bool PreValidate(ValidationContext<BridgeRecoverySection> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure(string.Empty, $"{nameof(BridgeRecoverySection)} object must not be null."));
                return false;
            }

            return true;
        }
    }
}
