﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CallMiner.Mining.Bridge.Api.Domain
{
    /// <summary>
    /// Represents a Bridge API recovery request record.
    /// </summary>
    public class RecoveryRequest
    {
        /// <summary>
        /// The recovery request Id.
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// The serialized RequestContext object.
        /// </summary>
        public string Payload { get; set; } = string.Empty;
    }
}
