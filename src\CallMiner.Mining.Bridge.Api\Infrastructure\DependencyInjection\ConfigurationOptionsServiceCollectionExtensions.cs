﻿using CallMiner.Mining.Bridge.Api.Configuration;
using CallMiner.ServiceConfiguration.Manager;
using CallMiner.ServiceConfiguration.Sections.BridgeRecovery;
using CallMiner.ServiceConfiguration.Sections.Constants;
using CallMiner.Validation.FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace CallMiner.Mining.Bridge.Api.Infrastructure.DependencyInjection
{
    /// <summary>
    /// Provides extension methods to register configuration options.
    /// </summary>
    public static class ConfigurationOptionsServiceCollectionExtensions
    {
        /// <summary>
        /// Register configuration options.
        /// </summary>
        /// <param name="services">Services collection.</param>
        /// <returns>The <see cref="IServiceCollection"/> so that additional calls can be chained.</returns>
        public static IServiceCollection RegisterConfigurationOptions(this IServiceCollection services)
        {
            var recoveryConfig = ServiceConfigurationManager.Get<BridgeRecoverySection>(ConfigurationSections.BridgeRecovery);

            // Validate web api config sections.
            var recoveryValidator = new BridgeRecoverySectionValidator();
            recoveryValidator.ValidateAndThrowCallMinerException(recoveryConfig, 400, ErrorGuids.InvalidOrMissingConfiguration,
                (flattenedErrorMessage) => BuildConfigurationFailedError(ConfigurationSections.CallMinerWebApi, flattenedErrorMessage));

            services.Configure<BridgeRecoverySection>(ServiceConfigurationManager.Configuration.GetSection(ConfigurationSections.BridgeRecovery));

            return services;
        }

        /// <summary>
        /// Builds the final error message for a config section that failed validation.
        /// </summary>
        /// <param name="sectionName">The name of the config section that failed validation.</param>
        /// <param name="flattenedErrorMessage">The flattened error message provided by a FluentValidator.</param>
        /// <returns></returns>
        private static string BuildConfigurationFailedError(string sectionName, string flattenedErrorMessage)
        {
            return $"The {sectionName} configuration failed validation: {Environment.NewLine}{flattenedErrorMessage}";
        }
    }
}