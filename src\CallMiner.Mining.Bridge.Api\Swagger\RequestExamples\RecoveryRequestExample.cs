﻿using CallMiner.Mining.Bridge.Api.Domain;
using Swashbuckle.AspNetCore.Filters;
using System;

namespace CallMiner.Mining.Bridge.Api
{
    /// <summary>
    /// Example RecoveryRequest
    /// </summary>
    public class RecoveryRequestExample : IExamplesProvider<RecoveryRequest>, ISwaggerExample
    {
        /// <summary>
        /// Return an example of the RecoveryRequest.
        /// </summary>
        /// <returns></returns>
        public RecoveryRequest GetExamples()
        {
            return new RecoveryRequest
            {
                Id = Guid.NewGuid().ToString(),
                Payload = "{\"InputFilePath\":\"\\\\\\\\cmdtnas01\\\\Development\\\\Environments\\\\CM6\\\\TestData\\\\testdata.wav\",\"OriginDate\":\"2019-08-30T15:48:21.7496741Z\",\"TenantApiKey\":\"devngms\",\"AudioSourceName\":\"five9sample\"}"
            };
        }
    }
}
