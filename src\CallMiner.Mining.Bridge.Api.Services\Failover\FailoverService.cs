﻿using CallMiner.Mining.Bridge.Api.Domain;
using CallMiner.Mining.Bridge.Api.Repositories;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Responsible for handling failover of the request received by the API.
    /// </summary>
    public class FailoverService : IFailoverService
    {
        private readonly IRecoveryDbRepository _miningBridgeFileInputRepository;

        /// <summary>
        /// Initializes a new instance of FailoverService class.
        /// </summary>
        public FailoverService(IRecoveryDbRepository miningBridgeFileInputRepository)
        {
            _miningBridgeFileInputRepository = miningBridgeFileInputRepository;
        }

        /// <summary>
        /// Insert a new request received by the API into the local recovery database instance.
        /// </summary>
        /// <param name="request">The request received by the API that will be serialized into json format.</param>
        public void InsertRequest(MiningBridgeSingleFileInputRequest request)
        {
            _miningBridgeFileInputRepository.InsertFileInput(request);
        }
    }
}
