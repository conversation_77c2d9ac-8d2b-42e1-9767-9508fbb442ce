﻿using CallMiner.Mining.Bridge.Api.Domain;
using System;
using System.Collections.Generic;

namespace CallMiner.Mining.Bridge.Api.Repositories
{
    /// <summary>
    /// Defines the contract for a IMiningBridgeFileInputRepository, which is responsible for inserting requests into the local recovery database.
    /// </summary>
    public interface IRecoveryDbRepository
    {
        /// <summary>
        /// Insert a new request received from the API into the local recovery database.
        /// </summary>
        /// <param name="request">The request received from the API that will be serialized into json format.</param>
        void InsertFileInput(MiningBridgeSingleFileInputRequest request);

        /// <summary>
        /// Will return a list of pending requests
        /// </summary>
        /// <returns>List of request objects</returns>
        /// <param name="limit">Maximum number of records returned</param>
        List<RecoveryRequest> GetPendingRequests(int limit);

        /// <summary>
        /// Will delete a specific request object from the local SQLite database instance after being succesfully posted
        /// </summary>
        /// <param name="request">The request to delete based on its Id</param>
        void DeleteRequest(RecoveryRequest request);

        /// <summary>
        /// Deletes all requests older than the maxRequestAge timestamp from the current time
        /// </summary>
        /// <param name="maxRequestAge">The largest lifetime a request can have</param>
        void PurgeOldRequests(TimeSpan maxRequestAge);

        /// <summary>
        /// Shrinks database after successful recovery
        /// </summary>
        void Shrink();

        /// <summary>
        /// Save remaining data for Docker
        /// </summary>
        void FlushAndStop();
    }
}
