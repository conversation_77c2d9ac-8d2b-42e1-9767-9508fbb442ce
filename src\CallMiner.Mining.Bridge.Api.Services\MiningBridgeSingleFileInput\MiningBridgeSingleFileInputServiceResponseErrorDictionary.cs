﻿using CallMiner;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Represents the possible known errors that can occur inside the MiningBridgeSingleFileInputService.
    /// </summary>
    public class MiningBridgeSingleFileInputServiceResponseErrorDictionary : BaseErrorMessages
    {
        /// <summary>
        /// Initializes a new instance of the MiningBridgeSingleFileInputServiceResponseErrorDictionary which provides localized error messages.
        /// </summary>
        public MiningBridgeSingleFileInputServiceResponseErrorDictionary()
        {
            AddMessage("eng", 101, "Mining bridge single file input request object cannot be null.", "a0eca8ec-c37c-4031-8bf3-dccefec7e7c9");
            AddMessage("eng", 102, "The InputFilePath property on the mining bridge single file input request object cannot be null or empty.", "ada954fd-12b7-4ca3-83a1-10ee0b0e953d");
            AddMessage("eng", 103, "Validation error: {0}", "983a7c94-2e53-408a-a91d-5298d31b604a");
            AddMessage("eng", 104, "The file: \"{0}\" could not be validated as a metadata or audio file type.", "a770fc8c-ac52-4c1b-81f5-25433a71fa6a");

        }
    }
}
