﻿using CallMiner.Mining.Bridge.Api.Domain;
using CallMiner.ServiceFramework.Domain;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Defines the contract for a IFailoverService, which is responsible for handling failover on publishing to a queue.
    /// </summary>
    public interface IFailoverService
    {
        /// <summary>
        /// Insert a new request received by the API into the local recovery database instance.
        /// </summary>
        /// <param name="request">The request received by the API that will be serialized into json format.</param>
        void InsertRequest(MiningBridgeSingleFileInputRequest request);
    }
}
