﻿using System;
using CallMiner.Mining.Bridge.Api.Domain;
using CallMiner.QueueMessagePublisher;
using System.Collections.Generic;
using System.IO;
using CallMiner.ServiceFramework.Domain;
using CallMiner.ServiceFramework.Domain.Constants;
using CallMiner.SystemMetadata.SDK;
using Newtonsoft.Json;
using CallMiner.NgmFile.Services;
using CallMiner.IO.Unc;

namespace CallMiner.Mining.Bridge.Api.Services
{
    /// <summary>
    /// Responsible for processing a mining bridge single file input request.
    /// </summary>
    public class MiningBridgeSingleFileInputService : IMiningBridgeSingleFileInputService
    {
        private readonly IPathFtpAccountNameExtractorService _pathFtpAccountNameExtractorService;
        private readonly IFailoverService _failoverService;
        private readonly IMessagePublisher _rabbitMqMessagePublisher;
        private readonly IAudioSourceLocatorService _audioSourceLocatorService;
        private readonly IVisibilityRequestService _visibilityRequestService;
        private readonly INgmFileWriterService _ngmFileWriterService;
        private readonly IGetFileTypeService _getFileTypeService;
        private readonly IXmlMetadataBuilderService _xmlMetadataBuilderService;
        private readonly IUncMapperService _uncMapperService;

        /// <summary>
        /// Initializes a new instance of MiningBridgeSingleFileInputService class.
        /// </summary>
        /// <param name="pathFtpAccountNameExtractorService">Service used to extract a Tenant's FTP account name from a path.</param>
        /// <param name="rabbitMqMessagePublisher">Rabbit publisher service.</param>
        /// <param name="failoverService">Failover service for handling errors on publishing to the input queue.</param>
        public MiningBridgeSingleFileInputService(IPathFtpAccountNameExtractorService pathFtpAccountNameExtractorService,
            IMessagePublisher rabbitMqMessagePublisher,
            IFailoverService failoverService,
            IAudioSourceLocatorService audioSourceLocatorService,
            IVisibilityRequestService visibilityRequestService,
            INgmFileWriterService ngmFileWriterService,
            IGetFileTypeService getFileTypeService,
            IXmlMetadataBuilderService xmlMetadataBuilderService,
            IUncMapperService uncMapperService)
        {
            _pathFtpAccountNameExtractorService = pathFtpAccountNameExtractorService;
            _rabbitMqMessagePublisher = rabbitMqMessagePublisher;
            _failoverService = failoverService;
            _audioSourceLocatorService = audioSourceLocatorService;
            _visibilityRequestService = visibilityRequestService;
            _ngmFileWriterService = ngmFileWriterService;
            _getFileTypeService = getFileTypeService;
            _xmlMetadataBuilderService = xmlMetadataBuilderService;
            _uncMapperService = uncMapperService;
        }

        /// <summary>
        /// Process a mining bridge single input request and inserts a file into the mining system to 
        /// start the metadata and audio pairing process for Tenants configured with an FTP account.
        /// </summary>
        /// <param name="request">The mining bridge single file input request to process.</param>
        /// <returns>A MiningBridgeSingleFileInputServiceResponse object.</returns>
        public MiningBridgeSingleFileInputServiceResponse Process(MiningBridgeSingleFileInputRequest request)
        {
            return HandleRequest(request, false);
        }

        /// <summary>
        /// Process a mining bridge single input request and inserts a file into the mining system to 
        /// start the metadata and audio pairing process for Tenants configured with an FTP account.
        /// </summary>
        /// <param name="request">RecoveryRequest wit serialized payload of the original context created for Rabbit MQ.</param>
        /// <returns>A MiningBridgeSingleFileInputServiceResponse object.</returns>
        public MiningBridgeSingleFileInputServiceResponse ProcessRecovery(RecoveryRequest request)
        {
            var requestContext = JsonConvert.DeserializeObject<MiningBridgeSingleFileInputRequest>(request.Payload);
            return HandleRequest(requestContext, true);
        }

        private static string ReturnPathWithoutPgpExtension(string filePath)
        {
            return Path.GetExtension(filePath).Equals(".pgp", StringComparison.OrdinalIgnoreCase) ?
                   Path.ChangeExtension(filePath, null) : filePath;
        }

        /// <summary>
        /// Process the MiningBridgeSingleFileInputRequest with the appropriate logic to handle pushing the original http request in
        /// it's entirety into the recovery db to be pushed back.
        /// </summary>
        /// <param name="request">A complete implementation of a MiningBridgeSingleFileInputRequest.</param>
        /// <param name="isRecoveryRequest">Defines if the request is a recovery request or not. If it is a recovery request then the database is not updated.</param>
        /// <returns></returns>
        private MiningBridgeSingleFileInputServiceResponse HandleRequest(MiningBridgeSingleFileInputRequest request, bool isRecoveryRequest)
        {
            Guid requestVisibilityId = _visibilityRequestService.NewCombGuid();

            // Validate request object is not null.
            if (request == null)
                return new MiningBridgeSingleFileInputServiceResponse(101);

            // Validate the request input file path is not empty.
            if (string.IsNullOrWhiteSpace(request.InputFilePath))
                return new MiningBridgeSingleFileInputServiceResponse(102);

            try
            {
                string tenantApiKey = request.TenantApiKey;
                string audioSourceName = request.AudioSourceName;

                // If the TenantApiKey was defined in the request then no need to find the owner based on the ftp path.
                if (string.IsNullOrWhiteSpace(request.TenantApiKey) || string.IsNullOrWhiteSpace(request.AudioSourceName))
                {
                    // Extract the FTP account from the path in the request.
                    var ftpAccountExtractResponse = _pathFtpAccountNameExtractorService.Extract(_uncMapperService.Map(request.InputFilePath));

                    if (ftpAccountExtractResponse.HasErrors)
                    {
                        // No FTP account found for given path, return error.
                        return new MiningBridgeSingleFileInputServiceResponse(ftpAccountExtractResponse);
                    }

                    // FTP account for a Tenant is configured.
                    TenantFtpAccountData tenantFtpAcountData = ftpAccountExtractResponse.Result;
                    tenantApiKey = tenantFtpAcountData.TenantApiKey;
                    audioSourceName = tenantFtpAcountData.AudioSourceName;
                }

                // grab the file type from the input file path.
                var fileType = _getFileTypeService.GetFileType(request.InputFilePath);
                var source = _audioSourceLocatorService.LocateAudioSourceByTenantAndSourceName(tenantApiKey, audioSourceName);

                // Build the mining file input object to publish to RabbitMQ.
                var correlationId = _visibilityRequestService.NewCombGuid();
                var contentVisibilityId = _visibilityRequestService.NewCombGuid();
                var ngmFilePath = $"{request.InputFilePath}.ngm";

                var contentItem = new Content()
                {
                    VisibilityId = contentVisibilityId,
                    FullFilePath = request.InputFilePath,
                    ContentType = ContentType.File,
                    IsPgpEncrypted = true
                };

                var content = new List<Content>() { contentItem };

                var routerRequest = new Request()
                {
                    RequestId = requestVisibilityId,
                    CorrelationId = correlationId,
                    AudioSourceName = audioSourceName,
                    TenantApiKey = tenantApiKey,
                    RequestContent = content,
                    RequestSource = RequestSource.BridgeApi,
                    RequestType = source.IngestionSource.Type.ToString(),
                    ContentType = ContentType.File,
                    ContentFilter = Path.GetExtension(ReturnPathWithoutPgpExtension(request.InputFilePath)),
                    CreateTimestamp = request.OriginDate,
                    NgmFilePath = ngmFilePath
                };

                if (fileType == FileType.Unknown)
                {
                    _visibilityRequestService.TrackRequest(routerRequest, source, true);
                    return new MiningBridgeSingleFileInputServiceResponse(104, new[] { request.InputFilePath });
                }

                // When dealing with an audio file where the source is audio only ingestion a metadata file should be generated.
                if (fileType == FileType.Audio && source.AudioOnlyIngestion)
                {
                    string mappedInputFilePath = _uncMapperService.Map(request.InputFilePath);
                    var metadataFilePath = _xmlMetadataBuilderService.BuildXmlMetadata(mappedInputFilePath);

                    // change the full file path to match the metadata file path.
                    // set the request input file path to the new metadata filename (use UNC version for Windows compatibility).
                    contentItem.FullFilePath = _uncMapperService.UnMap(metadataFilePath);
                    routerRequest.ContentFilter = Path.GetExtension(ReturnPathWithoutPgpExtension(contentItem.FullFilePath));
                }

                // Map the path to support Docker/K8s
                string mappedNgmFilePath = _uncMapperService.Map(ngmFilePath);

                // Create .ngm file with request id
                _ngmFileWriterService.InitializeNgmFile(mappedNgmFilePath, requestVisibilityId.ToString());

                // Publish the message into RabbitMQ.
                _rabbitMqMessagePublisher.Publish(routerRequest, routerRequest.RequestId);

                // send the visibility request to the very end so the visibility refernece is only created at the point that it is successfully
                // pushed into the mining system.
                _visibilityRequestService.TrackRequest(routerRequest, source);
            }
            // only validation errors are reported here
            catch (ArgumentException e)
            {
                return new MiningBridgeSingleFileInputServiceResponse(103, new[] { e.Message });
            }
            catch (CallMinerException e)
            {
                return new MiningBridgeSingleFileInputServiceResponse(103, new[] { e.Message });
            }
            catch
            {
                if (!isRecoveryRequest)
                {
                    // locally persist the request to be sent later, throw an exception to inform the occurrence of the error and also to log it
                    _failoverService.InsertRequest(request);
                }
                throw;
            }

            // Success, return the file input Id.
            return new MiningBridgeSingleFileInputServiceResponse(requestVisibilityId.ToString(), "Success");
        }
    }
}
